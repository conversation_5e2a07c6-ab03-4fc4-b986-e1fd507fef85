<?php
/**
 * API Payments Management สำหรับ Admin Tools
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT');
header('Access-Control-Allow-Headers: Content-Type');

session_start();

// ตรวจสอบ admin authentication
if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

require_once '../../includes/config.php';

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            // ดึงรายการการชำระเงิน
            $search = $_GET['search'] ?? '';
            $status = $_GET['status'] ?? '';
            
            $sql = "SELECT py.id, u.username, py.amount,
                           CASE
                               WHEN py.package_id IS NOT NULL THEN p.name
                               ELSE CONCAT('Package (', py.amount, ' THB)')
                           END as package_name,
                           py.status, py.payment_method, py.created_at, py.verified_at,
                           py.transaction_id, py.slip_image
                    FROM payments py
                    JOIN users u ON py.user_id = u.id
                    LEFT JOIN packages p ON py.package_id = p.id
                    WHERE 1=1";
            $params = [];
            
            if (!empty($search)) {
                $sql .= " AND (u.username LIKE ? OR py.transaction_id LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }
            
            if (!empty($status) && $status !== 'All') {
                $sql .= " AND py.status = ?";
                $params[] = strtolower($status);
            }
            
            $sql .= " ORDER BY py.created_at DESC";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $payments = $stmt->fetchAll();
            
            echo json_encode($payments);
            break;
            
        case 'PUT':
            // อัปเดตสถานะการชำระเงิน
            $data = json_decode(file_get_contents('php://input'), true);
            
            $payment_id = $data['id'] ?? '';
            $action = $data['action'] ?? ''; // 'verify' หรือ 'reject'
            $admin_notes = $data['notes'] ?? '';
            
            if (empty($payment_id) || empty($action)) {
                echo json_encode(['success' => false, 'message' => 'Payment ID and action required']);
                exit;
            }
            
            if ($action === 'verify') {
                // ยืนยันการชำระเงิน
                $stmt = $pdo->prepare("UPDATE payments SET status = 'verified', verified_at = NOW(), admin_notes = ? WHERE id = ?");
                $stmt->execute([$admin_notes, $payment_id]);
                
                // อัปเดตข้อมูลผู้ใช้
                $stmt = $pdo->prepare("SELECT user_id, package_id FROM payments WHERE id = ?");
                $stmt->execute([$payment_id]);
                $payment = $stmt->fetch();
                
                if ($payment) {
                    // ดึงข้อมูลแพ็กเกจ
                    $stmt = $pdo->prepare("SELECT duration_days FROM packages WHERE id = ?");
                    $stmt->execute([$payment['package_id']]);
                    $package = $stmt->fetch();
                    
                    if ($package) {
                        $expires_at = date('Y-m-d H:i:s', strtotime("+{$package['duration_days']} days"));
                        
                        // อัปเดตผู้ใช้
                        $stmt = $pdo->prepare("UPDATE users SET package_id = ?, expires_at = ?, status = 'active' WHERE id = ?");
                        $stmt->execute([$payment['package_id'], $expires_at, $payment['user_id']]);
                    }
                }
                
                echo json_encode(['success' => true, 'message' => 'Payment verified successfully']);
                
            } elseif ($action === 'reject') {
                // ปฏิเสธการชำระเงิน
                $stmt = $pdo->prepare("UPDATE payments SET status = 'failed', admin_notes = ? WHERE id = ?");
                $stmt->execute([$admin_notes, $payment_id]);
                
                echo json_encode(['success' => true, 'message' => 'Payment rejected']);
                
            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        case 'POST':
            // Auto check payments (ตรวจสอบอัตโนมัติ)
            $action = $_POST['action'] ?? '';
            
            if ($action === 'auto_check') {
                // เรียกใช้ PayNoi API เพื่อตรวจสอบการชำระเงิน
                require_once '../../includes/paynoi_api.php';
                
                $paynoi = new PayNoiAPI();
                $result = $paynoi->checkPendingPayments();
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Auto check completed',
                    'verified_count' => $result['verified_count'] ?? 0,
                    'failed_count' => $result['failed_count'] ?? 0
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Payments API Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
