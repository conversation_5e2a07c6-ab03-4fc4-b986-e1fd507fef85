#!/bin/bash

# Quick Fix for Cron Database Issues
# This script fixes the database table issues for cron jobs

echo "🎬 Jellyfin by James - Database Fix for Cron Jobs"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WEB_ROOT="/var/www/html"
PHP_BIN="/usr/bin/php"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root${NC}"
   echo "Usage: sudo ./fix_cron_database.sh"
   exit 1
fi

echo -e "${BLUE}🗄️  Setting up database tables for cron jobs...${NC}"

# Run database setup
if [ -f "$WEB_ROOT/setup_cron_database.php" ]; then
    echo -e "${BLUE}📋 Running database setup script...${NC}"
    $PHP_BIN "$WEB_ROOT/setup_cron_database.php"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Database setup completed successfully${NC}"
    else
        echo -e "${RED}❌ Database setup failed${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ Database setup script not found at $WEB_ROOT/setup_cron_database.php${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🧪 Testing cron scripts...${NC}"

# Test PayNoi script
echo -e "${YELLOW}Testing PayNoi transactions script...${NC}"
sudo -u www-data $PHP_BIN "$WEB_ROOT/paynoi-transactions.php" 2>&1 | head -5

echo ""
echo -e "${YELLOW}Testing manual check payments script...${NC}"
if [ -f "$WEB_ROOT/manual_check_payments.php" ]; then
    sudo -u www-data $PHP_BIN "$WEB_ROOT/manual_check_payments.php" 2>&1 | head -5
else
    echo -e "${YELLOW}⚠️  manual_check_payments.php not found${NC}"
fi

echo ""
echo -e "${YELLOW}Testing expiration check script...${NC}"
if [ -f "$WEB_ROOT/cron/check_expiration.php" ]; then
    sudo -u www-data $PHP_BIN "$WEB_ROOT/cron/check_expiration.php" 2>&1 | head -5
else
    echo -e "${YELLOW}⚠️  cron/check_expiration.php not found${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Database fix completed!${NC}"
echo ""
echo -e "${BLUE}📊 Next Steps:${NC}"
echo "1. Run the full cron setup: sudo ./setup_paynoi_cron_5sec.sh"
echo "2. Monitor logs: sudo tail -f /var/log/jellyfin/*.log"
echo "3. Check cron status: sudo systemctl status cron"
echo ""
echo -e "${GREEN}✅ Ready for automated cron jobs!${NC}"
