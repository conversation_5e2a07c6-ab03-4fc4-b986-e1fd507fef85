<?php
/**
 * Test PayNoi API Connection
 * ทดสอบการเชื่อมต่อ PayNoi API
 */

require_once 'includes/config.php';
require_once 'includes/PayNoiAPIClass.php';

echo "🔍 Testing PayNoi API Connection\n";
echo "================================\n\n";

try {
    // Initialize PayNoi API
    $paynoiAPI = new PayNoiAPI();
    
    echo "1. 📋 API Configuration:\n";
    echo "   API Key: " . substr($paynoiAPI->getApiKey(), 0, 20) . "...\n";
    echo "   Record Key: " . $paynoiAPI->getRecordKey() . "\n";
    echo "   API URL: https://paynoi.com/api_line\n\n";
    
    echo "2. 🔗 Testing API Connection...\n";
    
    // Test basic API call
    $response = $paynoiAPI->getTransactions();
    
    if (!$response) {
        echo "❌ API call failed - no response\n";
        echo "💡 Possible issues:\n";
        echo "   - Internet connection\n";
        echo "   - PayNoi API is down\n";
        echo "   - Invalid API credentials\n";
        exit(1);
    }
    
    echo "✅ API responded\n";
    echo "📊 Response structure:\n";
    
    if (is_array($response)) {
        echo "   Type: Array\n";
        echo "   Keys: " . implode(', ', array_keys($response)) . "\n";
        
        if (isset($response['data'])) {
            echo "   Data type: " . gettype($response['data']) . "\n";
            
            if (is_array($response['data'])) {
                echo "   Data count: " . count($response['data']) . "\n";
                
                // Show sample data structure
                if (!empty($response['data'])) {
                    $firstGroup = reset($response['data']);
                    if (is_array($firstGroup) && !empty($firstGroup)) {
                        $firstTransaction = reset($firstGroup);
                        echo "   Sample transaction keys: " . implode(', ', array_keys($firstTransaction)) . "\n";
                    }
                }
            }
        }
        
        if (isset($response['status'])) {
            echo "   Status: " . $response['status'] . "\n";
        }
        
        if (isset($response['message'])) {
            echo "   Message: " . $response['message'] . "\n";
        }
        
    } else {
        echo "   Type: " . gettype($response) . "\n";
        echo "   Content: " . substr(json_encode($response), 0, 200) . "...\n";
    }
    
    echo "\n3. 📊 Processing Transactions...\n";
    
    if (isset($response['data']) && is_array($response['data'])) {
        $totalTransactions = 0;
        $incomingTransactions = 0;
        $amounts = [];
        $dates = [];
        
        foreach ($response['data'] as $transactionGroup) {
            if (is_array($transactionGroup)) {
                foreach ($transactionGroup as $transaction) {
                    $totalTransactions++;
                    
                    $amount = floatval($transaction['amount'] ?? 0);
                    if ($amount > 0) {
                        $incomingTransactions++;
                        $amounts[] = $amount;
                        
                        if (isset($transaction['date'])) {
                            $dates[] = $transaction['date'];
                        }
                    }
                }
            }
        }
        
        echo "   Total transactions: {$totalTransactions}\n";
        echo "   Incoming transactions: {$incomingTransactions}\n";
        
        if (!empty($amounts)) {
            echo "   Amount range: " . min($amounts) . " - " . max($amounts) . " THB\n";
        }
        
        if (!empty($dates)) {
            sort($dates);
            echo "   Date range: " . reset($dates) . " to " . end($dates) . "\n";
        }
        
        // Show sample transactions
        echo "\n📋 Sample Transactions (first 5):\n";
        $sampleCount = 0;
        foreach ($response['data'] as $transactionGroup) {
            if (is_array($transactionGroup)) {
                foreach ($transactionGroup as $transaction) {
                    if ($sampleCount >= 5) break 2;
                    
                    $amount = $transaction['amount'] ?? 'N/A';
                    $date = $transaction['date'] ?? 'N/A';
                    $type = $transaction['type'] ?? 'N/A';
                    $bank = $transaction['bankaccount'] ?? 'N/A';
                    
                    echo "   #{$sampleCount}: {$amount} THB at {$date} ({$type}) - {$bank}\n";
                    $sampleCount++;
                }
            }
        }
        
    } else {
        echo "❌ No transaction data found\n";
        echo "Response: " . json_encode($response) . "\n";
    }
    
    echo "\n4. 🎯 Testing Recent Transactions...\n";
    
    // Test recent transactions method
    $recentResponse = $paynoiAPI->getRecentTransactions();
    
    if ($recentResponse && isset($recentResponse['data'])) {
        $recentCount = 0;
        foreach ($recentResponse['data'] as $group) {
            if (is_array($group)) {
                foreach ($group as $transaction) {
                    if (floatval($transaction['amount'] ?? 0) > 0) {
                        $recentCount++;
                    }
                }
            }
        }
        echo "   Recent incoming transactions (last hour): {$recentCount}\n";
    } else {
        echo "   ❌ Failed to get recent transactions\n";
    }
    
    echo "\n✅ PayNoi API Test Complete!\n";
    
    if ($incomingTransactions > 0) {
        echo "🎉 API is working correctly with {$incomingTransactions} incoming transactions\n";
        echo "💡 You can now run the slip matching scripts\n";
    } else {
        echo "⚠️  API is working but no incoming transactions found\n";
        echo "💡 This might be why slip matching isn't working\n";
        echo "   - Check if there are recent bank transfers\n";
        echo "   - Verify the time range\n";
        echo "   - Test with actual payments\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
