<?php
/**
 * Database Setup for Cron Jobs
 * Ensures all required tables exist for automated payment processing
 */

require_once 'includes/config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🎬 Jellyfin by James - Database Setup for Cron Jobs\n";
    echo "==================================================\n\n";
    
    // Check and create payment_transactions table
    $stmt = $pdo->query("SHOW TABLES LIKE 'payment_transactions'");
    if ($stmt->rowCount() == 0) {
        echo "❌ payment_transactions table not found. Creating...\n";
        
        $sql = "
        CREATE TABLE payment_transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            subscription_id INT NOT NULL,
            user_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_method VARCHAR(50) DEFAULT 'promptpay',
            transaction_ref VARCHAR(255),
            qr_code_url VARCHAR(500),
            slip_image VARCHAR(500),
            slip_hash VARCHAR(64) NULL,
            status ENUM('pending', 'pending_verification', 'completed', 'failed', 'cancelled', 'manual_review', 'verified') DEFAULT 'pending',
            paid_at TIMESTAMP NULL,
            verified_at TIMESTAMP NULL,
            admin_notes TEXT NULL,
            paynoi_transaction_id VARCHAR(255) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_subscription_id (subscription_id),
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            INDEX idx_transaction_ref (transaction_ref),
            INDEX idx_slip_hash (slip_hash),
            INDEX idx_verified_at (verified_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        echo "✅ payment_transactions table created successfully\n";
    } else {
        echo "✅ payment_transactions table exists\n";
        
        // Check if verified_at column exists
        $columns = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'verified_at'")->rowCount();
        if ($columns == 0) {
            echo "   Adding verified_at column...\n";
            $pdo->exec("ALTER TABLE payment_transactions ADD COLUMN verified_at TIMESTAMP NULL AFTER updated_at");
        }
        
        // Check if admin_notes column exists
        $columns = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'admin_notes'")->rowCount();
        if ($columns == 0) {
            echo "   Adding admin_notes column...\n";
            $pdo->exec("ALTER TABLE payment_transactions ADD COLUMN admin_notes TEXT NULL AFTER verified_at");
        }
        
        // Check if paynoi_transaction_id column exists
        $columns = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'paynoi_transaction_id'")->rowCount();
        if ($columns == 0) {
            echo "   Adding paynoi_transaction_id column...\n";
            $pdo->exec("ALTER TABLE payment_transactions ADD COLUMN paynoi_transaction_id VARCHAR(255) NULL AFTER admin_notes");
        }
        
        // Update status enum to include 'verified'
        echo "   Updating status enum...\n";
        $pdo->exec("ALTER TABLE payment_transactions MODIFY COLUMN status ENUM('pending', 'pending_verification', 'completed', 'failed', 'cancelled', 'manual_review', 'verified') DEFAULT 'pending'");
    }
    
    // Check user_subscriptions table
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_subscriptions'");
    if ($stmt->rowCount() == 0) {
        echo "❌ user_subscriptions table not found. Creating...\n";
        
        $sql = "
        CREATE TABLE user_subscriptions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            package_id INT NOT NULL,
            start_date TIMESTAMP NULL,
            end_date TIMESTAMP NULL,
            status ENUM('pending', 'active', 'expired', 'cancelled') DEFAULT 'pending',
            payment_amount DECIMAL(10,2) NOT NULL,
            payment_reference VARCHAR(255),
            qr_code_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_package_id (package_id),
            INDEX idx_status (status),
            INDEX idx_end_date (end_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        echo "✅ user_subscriptions table created successfully\n";
    } else {
        echo "✅ user_subscriptions table exists\n";
    }
    
    // Check packages table
    $stmt = $pdo->query("SHOW TABLES LIKE 'packages'");
    if ($stmt->rowCount() == 0) {
        echo "❌ packages table not found. Creating...\n";
        
        $sql = "
        CREATE TABLE packages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            duration_days INT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            max_simultaneous_sessions INT DEFAULT 1,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        echo "✅ packages table created successfully\n";
        
        // Insert default packages
        $pdo->exec("
            INSERT INTO packages (name, duration_days, price, max_simultaneous_sessions, description) VALUES
            ('15 วัน', 15, 60.00, 1, 'แพ็คเกจ 15 วัน'),
            ('30 วัน', 30, 100.00, 1, 'แพ็คเกจ 30 วัน'),
            ('150 บาท', 30, 150.00, 2, 'แพ็คเกจ 150 บาท - 2 อุปกรณ์')
        ");
        echo "✅ Default packages inserted\n";
    } else {
        echo "✅ packages table exists\n";
    }
    
    // Check activity_logs table
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
    if ($stmt->rowCount() == 0) {
        echo "❌ activity_logs table not found. Creating...\n";
        
        $sql = "
        CREATE TABLE activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        echo "✅ activity_logs table created successfully\n";
    } else {
        echo "✅ activity_logs table exists\n";
    }
    
    // Check if users table has required columns
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "✅ users table exists\n";
        
        // Check for jellyfin_user_id column
        $columns = $pdo->query("SHOW COLUMNS FROM users LIKE 'jellyfin_user_id'")->rowCount();
        if ($columns == 0) {
            echo "   Adding jellyfin_user_id column...\n";
            $pdo->exec("ALTER TABLE users ADD COLUMN jellyfin_user_id VARCHAR(255) NULL AFTER password_hash");
            $pdo->exec("ALTER TABLE users ADD INDEX idx_jellyfin_user_id (jellyfin_user_id)");
        }
        
        // Check for referred_by column
        $columns = $pdo->query("SHOW COLUMNS FROM users LIKE 'referred_by'")->rowCount();
        if ($columns == 0) {
            echo "   Adding referred_by column...\n";
            $pdo->exec("ALTER TABLE users ADD COLUMN referred_by INT NULL");
            $pdo->exec("ALTER TABLE users ADD INDEX idx_referred_by (referred_by)");
        }
        
        // Check for affiliate_points column
        $columns = $pdo->query("SHOW COLUMNS FROM users LIKE 'affiliate_points'")->rowCount();
        if ($columns == 0) {
            echo "   Adding affiliate_points column...\n";
            $pdo->exec("ALTER TABLE users ADD COLUMN affiliate_points DECIMAL(10,2) DEFAULT 0.00");
        }
    }
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "✅ All required tables and columns are now available for cron jobs\n";
    echo "\n📋 Tables verified:\n";
    echo "   - payment_transactions (with verified_at, admin_notes, paynoi_transaction_id)\n";
    echo "   - user_subscriptions\n";
    echo "   - packages (with default packages)\n";
    echo "   - activity_logs\n";
    echo "   - users (with jellyfin_user_id, referred_by, affiliate_points)\n";
    echo "\n🚀 Ready for automated cron jobs!\n";
    
} catch (Exception $e) {
    echo "❌ Database setup failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
