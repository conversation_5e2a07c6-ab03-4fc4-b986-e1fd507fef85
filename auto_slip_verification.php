<?php
/**
 * Automatic Slip Verification with PayNoi Matching
 * ตรวจสอบสลิปและจับคู่กับจำนวนเงินที่ดึงมาจาก PayNoi เพื่ออนุมัติอัตโนมัติ
 */

require_once 'includes/config.php';
require_once 'includes/PayNoiAPIClass.php';
require_once 'includes/JellyfinAPI.php';

// Logging function
function writeLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}";
    error_log($logMessage);
    echo $logMessage . "\n";
}

writeLog("=== Auto Slip Verification Started ===");

try {
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Initialize PayNoi API
    $paynoiAPI = new PayNoiAPI();
    
    // Initialize Jellyfin API
    $jellyfin = new JellyfinAPI();
    
    // Get pending payment transactions with slip images
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, u.jellyfin_user_id, u.referred_by,
               us.package_id, us.id as subscription_id,
               p.name as package_name, p.price as package_price, p.duration_days, p.max_simultaneous_sessions,
               r.username as referrer_username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        LEFT JOIN users r ON u.referred_by = r.id
        WHERE pt.status IN ('pending_verification', 'pending')
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.created_at ASC
        LIMIT 50
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $pendingCount = count($pendingPayments);
    writeLog("Found {$pendingCount} pending payments with slips to verify");
    
    if ($pendingCount == 0) {
        writeLog("=== Auto Slip Verification Completed: No pending payments ===");
        exit(0);
    }
    
    // Get PayNoi transactions from last 24 hours
    $startDate = date('Y-m-d H:i:s', strtotime('-24 hours'));
    $endDate = date('Y-m-d H:i:s');
    
    writeLog("Fetching PayNoi transactions from {$startDate} to {$endDate}");
    $paynoiTransactions = $paynoiAPI->getTransactions($startDate, $endDate);
    
    if (!$paynoiTransactions || !isset($paynoiTransactions['data'])) {
        writeLog("❌ Failed to fetch PayNoi transactions");
        exit(1);
    }
    
    // Flatten PayNoi transactions
    $flatTransactions = [];
    foreach ($paynoiTransactions['data'] as $transactionGroup) {
        foreach ($transactionGroup as $transaction) {
            if (floatval($transaction['amount'] ?? 0) > 0) { // Only incoming transactions
                $flatTransactions[] = $transaction;
            }
        }
    }
    
    $transactionCount = count($flatTransactions);
    writeLog("Found {$transactionCount} incoming PayNoi transactions");
    
    $verifiedCount = 0;
    $rejectedCount = 0;
    $duplicateCount = 0;
    
    foreach ($pendingPayments as $payment) {
        $paymentAmount = floatval($payment['amount']);
        $paymentTime = strtotime($payment['created_at']);
        $paymentId = $payment['id'];
        $username = $payment['username'];
        
        writeLog("Processing payment ID {$paymentId} - {$username} - {$paymentAmount} THB");
        
        // Check for duplicate slips first
        if (!empty($payment['slip_hash'])) {
            $dupStmt = $pdo->prepare("
                SELECT COUNT(*) as count FROM payment_transactions
                WHERE slip_hash = ? AND status IN ('completed', 'verified') AND id != ?
            ");
            $dupStmt->execute([$payment['slip_hash'], $paymentId]);
            $duplicateCheck = $dupStmt->fetch()['count'];
            
            if ($duplicateCheck > 0) {
                writeLog("❌ Duplicate slip detected for payment ID {$paymentId}");
                
                // Mark as failed
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions
                    SET status = 'failed',
                        admin_notes = 'Duplicate slip detected by auto-verification',
                        updated_at = NOW()
                    WHERE id = ?
                ");
                $updateStmt->execute([$paymentId]);
                $duplicateCount++;
                continue;
            }
        }
        
        // Find matching PayNoi transaction
        $matchingTransaction = null;
        $bestMatch = null;
        $bestScore = 0;
        
        foreach ($flatTransactions as $transaction) {
            $transactionAmount = floatval($transaction['amount'] ?? 0);
            $transactionTime = strtotime($transaction['date'] ?? '');
            
            // Check amount match (within 1.00 tolerance for random decimal)
            $amountDiff = abs($transactionAmount - $paymentAmount);
            if ($amountDiff > 1.00) {
                continue;
            }
            
            // Check time match (within 2 hours)
            $timeDiff = abs($transactionTime - $paymentTime);
            if ($timeDiff > 7200) { // 2 hours
                continue;
            }
            
            // Calculate match score (lower is better)
            $score = $amountDiff + ($timeDiff / 3600); // Amount diff + time diff in hours
            
            if ($score < $bestScore || $bestMatch === null) {
                $bestMatch = $transaction;
                $bestScore = $score;
            }
        }
        
        if ($bestMatch) {
            $transactionAmount = floatval($bestMatch['amount']);
            $amountDiff = abs($transactionAmount - $paymentAmount);
            $timeDiff = abs(strtotime($bestMatch['date']) - $paymentTime);
            $timeDiffMinutes = round($timeDiff / 60);
            
            writeLog("✅ Found matching transaction: Amount {$transactionAmount} THB (diff: {$amountDiff}), Time diff: {$timeDiffMinutes} minutes");
            
            // Determine if auto-verification is allowed
            $autoVerify = false;
            
            // Get user's verification history
            $historyStmt = $pdo->prepare("
                SELECT COUNT(*) as verified_count FROM payment_transactions
                WHERE user_id = ? AND status IN ('completed', 'verified')
            ");
            $historyStmt->execute([$payment['user_id']]);
            $verifiedCount_user = $historyStmt->fetch()['verified_count'];
            
            // Auto-verification rules
            if ($amountDiff <= 0.50 && $timeDiffMinutes <= 30) {
                // Exact or very close match within 30 minutes
                $autoVerify = true;
                writeLog("✅ Auto-verifying: Exact match within 30 minutes");
            } elseif ($paymentAmount <= 100.00 && $amountDiff <= 1.00 && $timeDiffMinutes <= 60) {
                // Small amounts with good match within 1 hour
                $autoVerify = true;
                writeLog("✅ Auto-verifying: Small amount with good match");
            } elseif ($verifiedCount_user >= 3 && $amountDiff <= 1.00 && $timeDiffMinutes <= 120) {
                // Trusted user (3+ previous payments) with reasonable match
                $autoVerify = true;
                writeLog("✅ Auto-verifying: Trusted user ({$verifiedCount_user} previous payments)");
            } else {
                writeLog("⚠️  Manual review required: Amount {$paymentAmount} THB, diff {$amountDiff}, time {$timeDiffMinutes}min, history {$verifiedCount_user}");
                
                // Mark for manual review
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions
                    SET status = 'manual_review',
                        admin_notes = CONCAT(COALESCE(admin_notes, ''), 'Auto-verification: Found PayNoi match but requires manual review. Amount diff: {$amountDiff} THB, Time diff: {$timeDiffMinutes} minutes'),
                        paynoi_transaction_id = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");
                $transactionId = $bestMatch['id'] ?? $bestMatch['trans_id'] ?? '';
                $updateStmt->execute([$transactionId, $paymentId]);
                continue;
            }
            
            if ($autoVerify) {
                try {
                    $pdo->beginTransaction();
                    
                    // Update payment status
                    $updateStmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET status = 'verified',
                            paynoi_transaction_id = ?,
                            verified_at = NOW(),
                            admin_notes = 'Auto-verified by slip verification cron',
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    $transactionId = $bestMatch['id'] ?? $bestMatch['trans_id'] ?? '';
                    $updateStmt->execute([$transactionId, $paymentId]);
                    
                    // Update subscription status
                    if ($payment['subscription_id']) {
                        $subscriptionStmt = $pdo->prepare("
                            UPDATE user_subscriptions
                            SET status = 'active',
                                start_date = NOW(),
                                end_date = DATE_ADD(NOW(), INTERVAL ? DAY),
                                updated_at = NOW()
                            WHERE id = ?
                        ");
                        $subscriptionStmt->execute([$payment['duration_days'], $payment['subscription_id']]);
                    }
                    
                    // Enable user in Jellyfin
                    if ($payment['jellyfin_user_id']) {
                        $jellyfinResult = $jellyfin->enableUser($payment['jellyfin_user_id']);
                        if ($jellyfinResult) {
                            writeLog("✅ Enabled Jellyfin user: {$username}");
                            
                            // Set simultaneous sessions limit
                            if ($payment['max_simultaneous_sessions']) {
                                $jellyfin->setUserSessionLimit($payment['jellyfin_user_id'], $payment['max_simultaneous_sessions']);
                            }
                        } else {
                            writeLog("⚠️  Failed to enable Jellyfin user: {$username}");
                        }
                    }
                    
                    // Award affiliate points if user was referred
                    if ($payment['referred_by'] && $payment['referrer_username']) {
                        $pointsToAward = 15; // 15 points for package purchase
                        
                        $affiliateStmt = $pdo->prepare("
                            UPDATE users
                            SET affiliate_points = affiliate_points + ?
                            WHERE id = ?
                        ");
                        $affiliateStmt->execute([$pointsToAward, $payment['referred_by']]);
                        
                        // Log affiliate transaction
                        $affiliateLogStmt = $pdo->prepare("
                            INSERT INTO referral_transactions (referrer_id, referred_id, points_earned, transaction_type, created_at)
                            VALUES (?, ?, ?, 'purchase', NOW())
                        ");
                        $affiliateLogStmt->execute([$payment['referred_by'], $payment['user_id'], $pointsToAward]);
                        
                        writeLog("✅ Awarded {$pointsToAward} affiliate points to {$payment['referrer_username']}");
                    }
                    
                    $pdo->commit();
                    $verifiedCount++;
                    
                    writeLog("✅ Payment ID {$paymentId} auto-verified successfully");
                    
                } catch (Exception $e) {
                    $pdo->rollBack();
                    writeLog("❌ Failed to verify payment ID {$paymentId}: " . $e->getMessage());
                    $rejectedCount++;
                }
            }
        } else {
            writeLog("❌ No matching PayNoi transaction found for payment ID {$paymentId}");
            
            // Check if payment is older than 2 hours - mark for manual review
            $paymentAge = time() - $paymentTime;
            if ($paymentAge > 7200) { // 2 hours
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions
                    SET status = 'manual_review',
                        admin_notes = 'Auto-verification: No matching PayNoi transaction found after 2 hours',
                        updated_at = NOW()
                    WHERE id = ?
                ");
                $updateStmt->execute([$paymentId]);
                writeLog("⚠️  Payment ID {$paymentId} marked for manual review (no PayNoi match after 2 hours)");
            }
        }
    }
    
    writeLog("=== Auto Slip Verification Completed ===");
    writeLog("✅ Verified: {$verifiedCount} payments");
    writeLog("❌ Rejected: {$rejectedCount} payments");
    writeLog("🔄 Duplicates: {$duplicateCount} payments");
    writeLog("📋 Total processed: " . ($verifiedCount + $rejectedCount + $duplicateCount) . " payments");
    
} catch (Exception $e) {
    writeLog("❌ Auto Slip Verification Error: " . $e->getMessage());
    exit(1);
}
?>
