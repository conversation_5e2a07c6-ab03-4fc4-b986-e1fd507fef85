<?php
/**
 * Debug PayNoi API in Detail
 * ตรวจสอบ PayNoi API อย่างละเอียดเพื่อหา transactions ที่หายไป
 */

require_once 'includes/config.php';
require_once 'includes/PayNoiAPIClass.php';

echo "🔍 Debug PayNoi API in Detail\n";
echo "=============================\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "1. 📋 Pending payments to match:\n";
    
    // Get pending payments
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.status IN ('pending_verification', 'pending')
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.created_at DESC
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $targetAmounts = [];
    foreach ($pendingPayments as $payment) {
        $amount = floatval($payment['amount']);
        $time = $payment['created_at'];
        echo "   - {$payment['username']}: {$amount} THB at {$time}\n";
        $targetAmounts[] = $amount;
    }
    
    echo "\n2. 🔗 Getting ALL PayNoi transactions (no filtering):\n";
    
    // Initialize PayNoi API
    $paynoiAPI = new PayNoiAPI();
    $response = $paynoiAPI->getTransactions();
    
    if (!$response || !isset($response['data'])) {
        echo "❌ Failed to get PayNoi data\n";
        exit(1);
    }
    
    echo "✅ PayNoi API responded\n";
    echo "📊 Raw response structure:\n";
    echo "   Data groups: " . count($response['data']) . "\n";
    
    // Process ALL transactions (no time filtering)
    $allTransactions = [];
    $transactionsByAmount = [];
    
    foreach ($response['data'] as $groupIndex => $transactionGroup) {
        echo "\n📦 Group #{$groupIndex}:\n";
        
        if (!is_array($transactionGroup)) {
            echo "   ⚠️  Not an array: " . gettype($transactionGroup) . "\n";
            continue;
        }
        
        echo "   Transactions in group: " . count($transactionGroup) . "\n";
        
        foreach ($transactionGroup as $transIndex => $transaction) {
            $amount = floatval($transaction['amount'] ?? 0);
            $date = $transaction['date'] ?? 'No date';
            $type = $transaction['type'] ?? 'No type';
            
            // Store all transactions
            $allTransactions[] = [
                'amount' => $amount,
                'date' => $date,
                'type' => $type,
                'raw' => $transaction
            ];
            
            // Group by amount for easy lookup
            if ($amount > 0) {
                if (!isset($transactionsByAmount[$amount])) {
                    $transactionsByAmount[$amount] = [];
                }
                $transactionsByAmount[$amount][] = $transaction;
            }
            
            // Show first few transactions in each group
            if ($transIndex < 3) {
                echo "      #{$transIndex}: {$amount} THB at {$date} ({$type})\n";
            }
        }
        
        if (count($transactionGroup) > 3) {
            echo "      ... and " . (count($transactionGroup) - 3) . " more\n";
        }
    }
    
    echo "\n3. 📊 Transaction Analysis:\n";
    echo "   Total transactions: " . count($allTransactions) . "\n";
    
    // Filter incoming transactions
    $incomingTransactions = array_filter($allTransactions, function($t) {
        return $t['amount'] > 0;
    });
    
    echo "   Incoming transactions: " . count($incomingTransactions) . "\n";
    
    if (count($incomingTransactions) > 0) {
        $amounts = array_column($incomingTransactions, 'amount');
        echo "   Amount range: " . min($amounts) . " - " . max($amounts) . " THB\n";
        
        // Show recent transactions
        echo "\n📋 Recent incoming transactions (last 20):\n";
        $recentTransactions = array_slice($incomingTransactions, 0, 20);
        foreach ($recentTransactions as $i => $transaction) {
            $timeAgo = 'Unknown';
            if ($transaction['date'] !== 'No date') {
                $transactionTime = strtotime($transaction['date']);
                if ($transactionTime) {
                    $timeAgo = round((time() - $transactionTime) / 3600, 1) . ' hours ago';
                }
            }
            echo "   #{$i}: {$transaction['amount']} THB at {$transaction['date']} ({$timeAgo})\n";
        }
    }
    
    echo "\n4. 🎯 Looking for exact matches:\n";
    
    $foundMatches = 0;
    foreach ($targetAmounts as $targetAmount) {
        echo "\n🔍 Searching for {$targetAmount} THB:\n";
        
        $exactMatches = [];
        $closeMatches = [];
        
        foreach ($incomingTransactions as $transaction) {
            $transactionAmount = $transaction['amount'];
            $amountDiff = abs($transactionAmount - $targetAmount);
            
            if ($amountDiff == 0) {
                $exactMatches[] = $transaction;
            } elseif ($amountDiff <= 1.00) {
                $closeMatches[] = [
                    'transaction' => $transaction,
                    'diff' => $amountDiff
                ];
            }
        }
        
        if (!empty($exactMatches)) {
            echo "   ✅ Found " . count($exactMatches) . " exact matches:\n";
            foreach ($exactMatches as $match) {
                $timeInfo = 'Unknown time';
                if ($match['date'] !== 'No date') {
                    $transactionTime = strtotime($match['date']);
                    if ($transactionTime) {
                        $timeAgo = round((time() - $transactionTime) / 3600, 1);
                        $timeInfo = "{$match['date']} ({$timeAgo} hours ago)";
                    }
                }
                echo "      - {$match['amount']} THB at {$timeInfo}\n";
            }
            $foundMatches++;
        } elseif (!empty($closeMatches)) {
            echo "   🔍 Found " . count($closeMatches) . " close matches (±1 THB):\n";
            foreach ($closeMatches as $match) {
                $transaction = $match['transaction'];
                $timeInfo = 'Unknown time';
                if ($transaction['date'] !== 'No date') {
                    $transactionTime = strtotime($transaction['date']);
                    if ($transactionTime) {
                        $timeAgo = round((time() - $transactionTime) / 3600, 1);
                        $timeInfo = "{$transaction['date']} ({$timeAgo} hours ago)";
                    }
                }
                echo "      - {$transaction['amount']} THB at {$timeInfo} (diff: {$match['diff']} THB)\n";
            }
        } else {
            echo "   ❌ No matches found\n";
        }
    }
    
    echo "\n5. 🕐 Time filtering analysis:\n";
    
    // Test different time ranges
    $timeRanges = [
        '1 hour' => 3600,
        '6 hours' => 21600,
        '24 hours' => 86400,
        '3 days' => 259200,
        '7 days' => 604800
    ];
    
    foreach ($timeRanges as $rangeName => $seconds) {
        $cutoffTime = time() - $seconds;
        $filteredCount = 0;
        
        foreach ($incomingTransactions as $transaction) {
            if ($transaction['date'] !== 'No date') {
                $transactionTime = strtotime($transaction['date']);
                if ($transactionTime && $transactionTime >= $cutoffTime) {
                    $filteredCount++;
                }
            }
        }
        
        echo "   {$rangeName}: {$filteredCount} transactions\n";
    }
    
    echo "\n6. 💡 Recommendations:\n";
    
    if ($foundMatches > 0) {
        echo "✅ Found matching transactions! The issue might be:\n";
        echo "   1. Time filtering is too strict (24 hours)\n";
        echo "   2. Date format parsing issues\n";
        echo "   3. Timezone differences\n";
        echo "\n💡 Try running with extended time range:\n";
        echo "   - Modify cutoff time to 3-7 days\n";
        echo "   - Check date format in PayNoi response\n";
        echo "   - Test manual matching without time filter\n";
    } else {
        echo "❌ No matching transactions found\n";
        echo "💡 Possible issues:\n";
        echo "   1. Transactions are older than available data\n";
        echo "   2. Amount format differences (decimal places)\n";
        echo "   3. PayNoi API doesn't include recent transactions\n";
        echo "   4. Different bank account or API scope\n";
    }
    
    echo "\n7. 🔧 Quick fix attempt:\n";
    
    if ($foundMatches > 0) {
        echo "Since we found matches, let's try to process them...\n";
        
        // Try to match and verify payments
        foreach ($pendingPayments as $payment) {
            $paymentAmount = floatval($payment['amount']);
            echo "\nProcessing {$payment['username']} - {$paymentAmount} THB:\n";
            
            // Look for exact matches
            $bestMatch = null;
            foreach ($incomingTransactions as $transaction) {
                if (abs($transaction['amount'] - $paymentAmount) <= 0.01) {
                    $bestMatch = $transaction;
                    break;
                }
            }
            
            if ($bestMatch) {
                echo "   ✅ Found match: {$bestMatch['amount']} THB\n";
                echo "   🔧 Auto-verifying payment...\n";
                
                // Update payment status
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions 
                    SET status = 'verified', 
                        verified_at = NOW(),
                        admin_notes = ?
                    WHERE id = ?
                ");
                $adminNotes = "Auto-verified: Exact PayNoi match found ({$bestMatch['amount']} THB at {$bestMatch['date']})";
                $updateStmt->execute([$adminNotes, $payment['id']]);
                
                echo "   ✅ Payment verified successfully!\n";
            } else {
                echo "   ❌ No exact match found\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
