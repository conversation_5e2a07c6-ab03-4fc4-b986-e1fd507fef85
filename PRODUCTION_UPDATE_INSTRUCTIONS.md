# 🚀 Production Server Update Instructions

## 🎯 **ปัญหาที่พบ:**
Production server ยังใช้ไฟล์เก่าที่มีปัญหา:
- ❌ `paynoi-transactions.php` ยังหา `payments` table แทน `payment_transactions`
- ❌ `cron/check_expiration.php` ยังใช้ `file_put_contents()` ที่มีปัญหา permission
- ❌ Database tables ยังไม่ครบครัน

## 📤 **ขั้นตอนการอัปเดต:**

### **1. อัปโหลดไฟล์ที่แก้ไขแล้ว:**
```bash
# อัปโหลดไฟล์หลักที่แก้ไขแล้ว
scp paynoi-transactions.php user@*************:/var/www/html/
scp cron/check_expiration.php user@*************:/var/www/html/cron/

# อัปโหลดไฟล์ database setup
scp setup_cron_database.php user@*************:/var/www/html/
scp fix_cron_database.sh user@*************:/var/www/html/

# อัปโหลดไฟล์ setup ที่อัปเดตแล้ว
scp setup_paynoi_cron_5sec.sh user@*************:/var/www/html/

# อัปโหลด documentation
scp INSTALL_PAYNOI_CRON_5SEC.md user@*************:/var/www/html/
scp CRON_FIXES_SUMMARY.md user@*************:/var/www/html/
```

### **2. เข้าสู่ Production Server:**
```bash
ssh user@*************
cd /var/www/html
```

### **3. รันการแก้ไข Database:**
```bash
# ให้สิทธิ์ execute
chmod +x fix_cron_database.sh
chmod +x setup_paynoi_cron_5sec.sh

# รัน database setup
sudo php setup_cron_database.php
```

### **4. ทดสอบไฟล์ที่แก้ไขแล้ว:**
```bash
# ทดสอบ PayNoi script
sudo -u www-data php paynoi-transactions.php

# ทดสอบ expiration script  
sudo -u www-data php cron/check_expiration.php

# ทดสอบ manual check script
sudo -u www-data php manual_check_payments.php
```

### **5. ติดตั้งระบบ Cron ใหม่:**
```bash
# รันการติดตั้งระบบอัตโนมัติครบครัน
sudo ./setup_paynoi_cron_5sec.sh
```

---

## 🔍 **ผลลัพธ์ที่คาดหวัง:**

### **หลังรัน setup_cron_database.php:**
```
🎬 Jellyfin by James - Database Setup for Cron Jobs
==================================================

✅ payment_transactions table exists
   Adding verified_at column...
   Adding admin_notes column...
   Adding paynoi_transaction_id column...
   Updating status enum...
✅ user_subscriptions table exists
✅ packages table exists
✅ activity_logs table exists
✅ users table exists
   Adding jellyfin_user_id column...
   Adding referred_by column...
   Adding affiliate_points column...

🎉 Database setup completed successfully!
```

### **หลังทดสอบ paynoi-transactions.php:**
```
PayNoi Transactions Sync - Started at 2025-07-08 22:15:00
Fetched 18 transactions from PayNoi API
Found X pending payments to verify
PayNoi Transactions Sync - Completed: X payments verified
```

### **หลังทดสอบ check_expiration.php:**
```
[2025-07-08 22:15:00] === Expiration Check Started ===
[2025-07-08 22:15:00] Found 0 expired subscriptions
[2025-07-08 22:15:00] === Expiration Check Completed ===
```

### **หลังรัน setup_paynoi_cron_5sec.sh:**
```
🎬 Jellyfin by James - Complete Automation Setup
===============================================

🗄️  Setting up database tables...
✅ Database setup completed

⏰ Creating cron jobs for every 5 seconds...
✅ Cron jobs installed successfully

🎉 Complete automation system installed successfully!

📊 System Status:
✅ PayNoi Transaction Verification: Every 5 seconds (720/hour)
✅ Manual Slip Verification: Every 5 seconds (720/hour)  
✅ User Expiration Check: Every 1 minute (60/hour)
✅ System Health Monitor: Every 15 minutes (4/hour)
✅ Automated Maintenance: Daily/Weekly

🚀 Jellyfin by James is now 100% automated!
```

---

## 📊 **ตรวจสอบการทำงาน:**

### **ดู Cron Jobs ที่ติดตั้ง:**
```bash
sudo crontab -u www-data -l | head -20
```

### **ตรวจสอบ Logs:**
```bash
# ดู logs แบบ real-time
sudo tail -f /var/log/jellyfin/payment_cron.log
sudo tail -f /var/log/jellyfin/manual_cron.log  
sudo tail -f /var/log/jellyfin/expiration_cron.log

# ดู system logs
sudo journalctl -u cron -f
```

### **ตรวจสอบ Database:**
```bash
# เข้า MySQL
mysql -u root -p jellyfin_registration

# ตรวจสอบตาราง
SHOW TABLES;
DESCRIBE payment_transactions;
SELECT COUNT(*) FROM payment_transactions WHERE status = 'pending';
```

---

## 🎬 **ผลลัพธ์สุดท้าย:**

หลังจากทำตามขั้นตอนทั้งหมด ระบบ **Jellyfin by James** จะมี:

✅ **PayNoi Auto-Verification** - ทุก 5 วินาที  
✅ **Manual Slip Matching** - ทุก 5 วินาที  
✅ **User Expiration Management** - ทุกนาที  
✅ **System Health Monitoring** - ทุก 15 นาที  
✅ **Database Structure** - ครบครันและพร้อมใช้งาน  
✅ **Error-Free Logging** - ใช้ system logging  

**🚀 ระบบอัตโนมัติ 100% พร้อมใช้งาน!**
