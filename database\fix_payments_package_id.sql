-- Fix payments table by adding package_id column
-- This fixes the SQL error: Unknown column 'py.package_id' in 'ON'

USE jellyfin_registration;

-- Add package_id column to payments table if it doesn't exist
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS package_id INT NULL AFTER amount;

-- Add index for better performance
ALTER TABLE payments 
ADD INDEX IF NOT EXISTS idx_package_id (package_id);

-- Add foreign key constraint to packages table
ALTER TABLE payments 
ADD CONSTRAINT IF NOT EXISTS fk_payments_package_id 
FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE SET NULL;

-- Show updated table structure
DESCRIBE payments;

-- Show current payments
SELECT id, user_id, amount, package_id, status, created_at 
FROM payments 
ORDER BY created_at DESC 
LIMIT 5;
