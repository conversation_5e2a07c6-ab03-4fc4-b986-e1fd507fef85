<?php
/**
 * Check PayNoi API Data
 * ตรวจสอบข้อมูลที่ PayNoi API ส่งมา
 */

require_once 'includes/config.php';
require_once 'includes/PayNoiAPIClass.php';

echo "🔍 Checking PayNoi API Data\n";
echo "===========================\n\n";

try {
    // Initialize PayNoi API
    $paynoiAPI = new PayNoiAPI();
    
    echo "1. 📡 Testing PayNoi API connection...\n";
    
    // Test different time ranges
    $timeRanges = [
        '1 hour' => ['-1 hour', 'now'],
        '6 hours' => ['-6 hours', 'now'],
        '24 hours' => ['-24 hours', 'now'],
        '3 days' => ['-3 days', 'now']
    ];
    
    foreach ($timeRanges as $rangeName => $range) {
        $startDate = date('Y-m-d H:i:s', strtotime($range[0]));
        $endDate = date('Y-m-d H:i:s', strtotime($range[1]));
        
        echo "\n📅 Testing {$rangeName} range ({$startDate} to {$endDate}):\n";
        
        $transactions = $paynoiAPI->getTransactions($startDate, $endDate);
        
        if (!$transactions) {
            echo "   ❌ No response from PayNoi API\n";
            continue;
        }
        
        if (!isset($transactions['data'])) {
            echo "   ❌ Invalid response format\n";
            echo "   Response: " . json_encode($transactions) . "\n";
            continue;
        }
        
        // Count transactions
        $totalTransactions = 0;
        $incomingTransactions = 0;
        $outgoingTransactions = 0;
        $amounts = [];
        
        foreach ($transactions['data'] as $transactionGroup) {
            foreach ($transactionGroup as $transaction) {
                $totalTransactions++;
                $amount = floatval($transaction['amount'] ?? 0);
                
                if ($amount > 0) {
                    $incomingTransactions++;
                    $amounts[] = $amount;
                } else {
                    $outgoingTransactions++;
                }
            }
        }
        
        echo "   📊 Total transactions: {$totalTransactions}\n";
        echo "   📈 Incoming: {$incomingTransactions}\n";
        echo "   📉 Outgoing: {$outgoingTransactions}\n";
        
        if ($incomingTransactions > 0) {
            echo "   💰 Amount range: " . min($amounts) . " - " . max($amounts) . " THB\n";
            
            // Show sample transactions
            echo "   📋 Sample incoming transactions:\n";
            $sampleCount = 0;
            foreach ($transactions['data'] as $transactionGroup) {
                foreach ($transactionGroup as $transaction) {
                    if (floatval($transaction['amount'] ?? 0) > 0 && $sampleCount < 3) {
                        $transactionTime = date('Y-m-d H:i:s', strtotime($transaction['date'] ?? ''));
                        echo "      - {$transaction['amount']} THB at {$transactionTime} ({$transaction['type']})\n";
                        $sampleCount++;
                    }
                }
                if ($sampleCount >= 3) break;
            }
        }
    }
    
    echo "\n2. 🗄️ Checking database pending payments...\n";
    
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get pending payments with details
    $stmt = $pdo->prepare("
        SELECT pt.id, pt.amount, pt.created_at, pt.status, pt.slip_image,
               u.username, u.phone,
               p.name as package_name, p.price as package_price
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status IN ('pending_verification', 'pending')
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   📋 Found " . count($pendingPayments) . " pending payments:\n";
    
    foreach ($pendingPayments as $payment) {
        $paymentTime = date('Y-m-d H:i:s', strtotime($payment['created_at']));
        $hasSlip = !empty($payment['slip_image']) ? 'Yes' : 'No';
        
        echo "      - ID {$payment['id']}: {$payment['username']} - {$payment['amount']} THB\n";
        echo "        Created: {$paymentTime}, Status: {$payment['status']}, Has Slip: {$hasSlip}\n";
        echo "        Package: {$payment['package_name']} ({$payment['package_price']} THB)\n";
        
        // Check if amount includes random decimal
        $amount = floatval($payment['amount']);
        $decimal = $amount - floor($amount);
        if ($decimal > 0) {
            echo "        ✅ Has random decimal: " . number_format($decimal, 2) . "\n";
        } else {
            echo "        ⚠️  No random decimal (might be hard to match)\n";
        }
        echo "\n";
    }
    
    echo "3. 🔍 Manual matching test...\n";
    
    if (!empty($pendingPayments)) {
        // Get recent PayNoi transactions
        $startDate = date('Y-m-d H:i:s', strtotime('-24 hours'));
        $endDate = date('Y-m-d H:i:s');
        $transactions = $paynoiAPI->getTransactions($startDate, $endDate);
        
        if ($transactions && isset($transactions['data'])) {
            // Flatten transactions
            $flatTransactions = [];
            foreach ($transactions['data'] as $transactionGroup) {
                foreach ($transactionGroup as $transaction) {
                    if (floatval($transaction['amount'] ?? 0) > 0) {
                        $flatTransactions[] = $transaction;
                    }
                }
            }
            
            echo "   Testing first pending payment against PayNoi transactions...\n";
            $testPayment = $pendingPayments[0];
            $paymentAmount = floatval($testPayment['amount']);
            $paymentTime = strtotime($testPayment['created_at']);
            
            echo "   Payment: {$testPayment['username']} - {$paymentAmount} THB at " . date('Y-m-d H:i:s', $paymentTime) . "\n";
            
            $matches = 0;
            foreach ($flatTransactions as $transaction) {
                $transactionAmount = floatval($transaction['amount'] ?? 0);
                $transactionTime = strtotime($transaction['date'] ?? '');
                
                $amountDiff = abs($transactionAmount - $paymentAmount);
                $timeDiff = abs($transactionTime - $paymentTime);
                $timeDiffHours = round($timeDiff / 3600, 1);
                
                // Show all transactions within reasonable range for debugging
                if ($amountDiff <= 5.00 && $timeDiff <= 86400) { // 5 THB and 24 hours
                    $transactionTimeStr = date('Y-m-d H:i:s', $transactionTime);
                    echo "      Potential: {$transactionAmount} THB at {$transactionTimeStr}\n";
                    echo "        Amount diff: {$amountDiff} THB, Time diff: {$timeDiffHours} hours\n";
                    
                    if ($amountDiff <= 1.00 && $timeDiff <= 7200) {
                        echo "        ✅ MATCH! (within tolerance)\n";
                        $matches++;
                    } else {
                        echo "        ❌ Outside tolerance\n";
                    }
                    echo "\n";
                }
            }
            
            if ($matches == 0) {
                echo "   ❌ No matches found for test payment\n";
                echo "   💡 Possible issues:\n";
                echo "      - PayNoi transactions are not recent enough\n";
                echo "      - Amount differences are too large\n";
                echo "      - Time differences are too large\n";
                echo "      - Payment amount doesn't match any PayNoi transaction\n";
            } else {
                echo "   ✅ Found {$matches} potential matches\n";
            }
        }
    }
    
    echo "\n4. 📊 Summary and Recommendations:\n";
    echo "==================================\n";
    
    // Get the most recent PayNoi data
    $recentTransactions = $paynoiAPI->getTransactions(
        date('Y-m-d H:i:s', strtotime('-1 hour')),
        date('Y-m-d H:i:s')
    );
    
    $recentCount = 0;
    if ($recentTransactions && isset($recentTransactions['data'])) {
        foreach ($recentTransactions['data'] as $group) {
            foreach ($group as $transaction) {
                if (floatval($transaction['amount'] ?? 0) > 0) {
                    $recentCount++;
                }
            }
        }
    }
    
    echo "Recent PayNoi transactions (last hour): {$recentCount}\n";
    echo "Pending payments: " . count($pendingPayments) . "\n";
    echo "\n";
    
    if ($recentCount == 0) {
        echo "🔍 Issue: No recent PayNoi transactions\n";
        echo "💡 Solutions:\n";
        echo "   1. Check if PayNoi API is working correctly\n";
        echo "   2. Verify API credentials and permissions\n";
        echo "   3. Check if there are actual bank transactions\n";
        echo "   4. Test with a longer time range\n";
    } elseif (count($pendingPayments) == 0) {
        echo "✅ No pending payments to process\n";
    } else {
        echo "🔍 Both PayNoi transactions and pending payments exist\n";
        echo "💡 Next steps:\n";
        echo "   1. Run: php debug_slip_matching.php\n";
        echo "   2. Check tolerance settings in auto_slip_verification.php\n";
        echo "   3. Verify payment amounts include random decimals\n";
        echo "   4. Test manual verification: php auto_slip_verification.php\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
