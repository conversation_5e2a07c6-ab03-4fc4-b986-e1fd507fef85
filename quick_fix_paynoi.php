<?php
/**
 * Quick Fix PayNoi API Issues
 * แก้ไขปัญหา PayNoi API อย่างรวดเร็ว
 */

require_once 'includes/config.php';
require_once 'includes/PayNoiAPIClass.php';

echo "🔧 Quick Fix PayNoi API Issues\n";
echo "==============================\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "1. 📋 Checking pending payments...\n";
    
    // Get pending payments
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, u.phone, u.jellyfin_user_id,
               us.package_id, us.id as subscription_id,
               p.name as package_name, p.price as package_price, p.duration_days
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status IN ('pending_verification', 'pending')
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.created_at DESC
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   Found " . count($pendingPayments) . " pending payments\n\n";
    
    if (count($pendingPayments) == 0) {
        echo "✅ No pending payments to process\n";
        exit(0);
    }
    
    // Show pending payments
    foreach ($pendingPayments as $i => $payment) {
        echo "   Payment #" . ($i + 1) . ": {$payment['username']} - {$payment['amount']} THB\n";
        echo "      Created: {$payment['created_at']}\n";
        echo "      Status: {$payment['status']}\n\n";
    }
    
    echo "2. 🔗 Testing PayNoi API...\n";
    
    // Initialize PayNoi API
    $paynoiAPI = new PayNoiAPI();
    
    // Test API connection
    $response = $paynoiAPI->getTransactions();
    
    if (!$response) {
        echo "❌ PayNoi API failed - trying alternative approach\n";
        
        // Manual verification approach
        echo "\n🔧 Manual Verification Approach:\n";
        echo "Since PayNoi API is not working, let's manually verify payments\n\n";
        
        foreach ($pendingPayments as $payment) {
            echo "Processing Payment #{$payment['id']} - {$payment['username']}:\n";
            
            // Check if payment has slip image
            if (empty($payment['slip_image'])) {
                echo "   ❌ No slip image uploaded\n";
                continue;
            }
            
            // Auto-verify based on simple rules
            $paymentAmount = floatval($payment['amount']);
            $paymentAge = time() - strtotime($payment['created_at']);
            $paymentAgeHours = round($paymentAge / 3600, 1);
            
            echo "   💰 Amount: {$paymentAmount} THB\n";
            echo "   ⏰ Age: {$paymentAgeHours} hours\n";
            echo "   📎 Has slip: Yes\n";
            
            // Simple auto-verification rules
            $autoVerify = false;
            $reason = "";
            
            if ($paymentAmount <= 100.00 && $paymentAgeHours <= 24) {
                $autoVerify = true;
                $reason = "Small amount with slip within 24 hours";
            } elseif ($paymentAgeHours <= 2) {
                $autoVerify = true;
                $reason = "Recent payment with slip";
            } else {
                // Check user history
                $historyStmt = $pdo->prepare("
                    SELECT COUNT(*) as verified_count FROM payment_transactions
                    WHERE user_id = ? AND status IN ('completed', 'verified')
                ");
                $historyStmt->execute([$payment['user_id']]);
                $verifiedCount = $historyStmt->fetch()['verified_count'];
                
                if ($verifiedCount >= 2) {
                    $autoVerify = true;
                    $reason = "Trusted user ({$verifiedCount} previous payments)";
                } else {
                    $reason = "Manual review needed (new user, large amount, or old payment)";
                }
            }
            
            if ($autoVerify) {
                echo "   ✅ AUTO-VERIFYING: {$reason}\n";
                
                // Update payment status
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions 
                    SET status = 'verified', 
                        verified_at = NOW(),
                        admin_notes = ?
                    WHERE id = ?
                ");
                $adminNotes = "Auto-verified (PayNoi API unavailable): {$reason}";
                $updateStmt->execute([$adminNotes, $payment['id']]);
                
                // Enable Jellyfin user if needed
                if (!empty($payment['jellyfin_user_id'])) {
                    try {
                        require_once 'includes/JellyfinAPI.php';
                        $jellyfinAPI = new JellyfinAPI();
                        $result = $jellyfinAPI->enableUser($payment['jellyfin_user_id']);
                        if ($result) {
                            echo "   🎬 Enabled Jellyfin user\n";
                        } else {
                            echo "   ⚠️  Failed to enable Jellyfin user\n";
                        }
                    } catch (Exception $e) {
                        echo "   ⚠️  Jellyfin API error: " . $e->getMessage() . "\n";
                    }
                }
                
                // Update subscription dates
                if ($payment['subscription_id']) {
                    $subscriptionStmt = $pdo->prepare("
                        UPDATE user_subscriptions 
                        SET start_date = NOW(), 
                            end_date = DATE_ADD(NOW(), INTERVAL ? DAY),
                            status = 'active'
                        WHERE id = ?
                    ");
                    $subscriptionStmt->execute([
                        $payment['duration_days'] ?? 30,
                        $payment['subscription_id']
                    ]);
                    echo "   📅 Updated subscription ({$payment['duration_days']} days)\n";
                }
                
                // Award affiliate points if referred
                $referrerStmt = $pdo->prepare("SELECT referred_by FROM users WHERE id = ?");
                $referrerStmt->execute([$payment['user_id']]);
                $referrer = $referrerStmt->fetch();
                
                if ($referrer && $referrer['referred_by']) {
                    $pointsStmt = $pdo->prepare("
                        UPDATE users 
                        SET affiliate_points = affiliate_points + 15 
                        WHERE id = ?
                    ");
                    $pointsStmt->execute([$referrer['referred_by']]);
                    
                    // Log referral transaction
                    try {
                        $refLogStmt = $pdo->prepare("
                            INSERT INTO referral_transactions (referrer_id, referred_user_id, points_awarded, transaction_type, created_at)
                            VALUES (?, ?, 15, 'package_purchase', NOW())
                        ");
                        $refLogStmt->execute([$referrer['referred_by'], $payment['user_id']]);
                        echo "   🎁 Awarded 15 affiliate points\n";
                    } catch (Exception $e) {
                        echo "   ⚠️  Affiliate points error: " . $e->getMessage() . "\n";
                    }
                }
                
                echo "   ✅ Payment verified successfully!\n";
                
            } else {
                echo "   ⚠️  MANUAL REVIEW: {$reason}\n";
                
                // Update to manual review status
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions 
                    SET status = 'manual_review',
                        admin_notes = ?
                    WHERE id = ?
                ");
                $adminNotes = "Manual review needed (PayNoi API unavailable): {$reason}";
                $updateStmt->execute([$adminNotes, $payment['id']]);
            }
            
            echo "\n";
        }
        
        echo "🎯 Manual verification complete!\n";
        echo "💡 To fix PayNoi API:\n";
        echo "   1. Check API credentials in PayNoiAPIClass.php\n";
        echo "   2. Test API manually: php test_paynoi_api.php\n";
        echo "   3. Contact PayNoi support if needed\n";
        
        exit(0);
    }
    
    echo "✅ PayNoi API is working!\n";
    echo "📊 Response received, processing transactions...\n";
    
    // If API works, use the normal flow
    if (isset($response['data']) && is_array($response['data'])) {
        // Flatten and filter transactions
        $flatTransactions = [];
        $cutoffTime = strtotime('-24 hours');
        
        foreach ($response['data'] as $transactionGroup) {
            foreach ($transactionGroup as $transaction) {
                if (floatval($transaction['amount'] ?? 0) > 0) {
                    $transactionTime = strtotime($transaction['date'] ?? '');
                    if ($transactionTime >= $cutoffTime) {
                        $flatTransactions[] = $transaction;
                    }
                }
            }
        }
        
        echo "   Found " . count($flatTransactions) . " recent PayNoi transactions\n";
        
        if (count($flatTransactions) > 0) {
            echo "✅ PayNoi API working correctly!\n";
            echo "💡 Now run: php fix_slip_matching.php\n";
        } else {
            echo "⚠️  No recent PayNoi transactions found\n";
            echo "💡 This might be why slip matching isn't working\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
