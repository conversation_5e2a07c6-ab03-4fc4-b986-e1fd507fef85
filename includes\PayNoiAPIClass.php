<?php
/**
 * PayNoi API Integration Class
 * Handles communication with PayNoi API for transaction verification
 */

class PayNoiAPI {
    private $apiKey;
    private $recordKey;
    private $apiUrl;
    private $timeout;

    public function __construct($apiKey = null, $recordKey = null) {
        $this->apiKey = $apiKey ?: '6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446';
        $this->recordKey = $recordKey ?: '100568';
        $this->apiUrl = 'https://paynoi.com/api_line';
        $this->timeout = 30;
    }

    /**
     * Get API Key (for testing purposes)
     */
    public function getApiKey() {
        return $this->apiKey;
    }

    /**
     * Get Record Key (for testing purposes)
     */
    public function getRecordKey() {
        return $this->recordKey;
    }

    /**
     * Set API key
     * @param string $apiKey
     */
    public function setApiKey($apiKey) {
        $this->apiKey = $apiKey;
    }

    /**
     * Set record key
     * @param string $recordKey
     */
    public function setRecordKey($recordKey) {
        $this->recordKey = $recordKey;
    }

    /**
     * Set API URL
     * @param string $apiUrl
     */
    public function setApiUrl($apiUrl) {
        $this->apiUrl = $apiUrl;
    }

    /**
     * Set timeout
     * @param int $timeout
     */
    public function setTimeout($timeout) {
        $this->timeout = $timeout;
    }

    /**
     * Check if API is properly configured
     * @return bool
     */
    public function isConfigured() {
        return !empty($this->apiKey);
    }

    /**
     * Make API request with data
     * @param array $data Request data
     * @return array|false
     */
    public function makeRequest($data) {
        $url = $this->apiUrl;

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($data),
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/x-www-form-urlencoded'
            ]
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            error_log('PayNoi API cURL Error: ' . $error);
            return false;
        }

        if ($httpCode !== 200) {
            error_log('PayNoi API HTTP Error ' . $httpCode . ': ' . $response);
            return false;
        }

        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('PayNoi API JSON Error: ' . json_last_error_msg());
            return false;
        }

        return $data;
    }
    
    /**
     * Get transactions from PayNoi API
     * @param string $recordKey Record key for pagination (optional)
     * @return array|false
     */
    public function getTransactions($recordKey = null) {
        // Use the correct PayNoi API URL format
        $url = 'https://paynoi.com/api_line?api_key=' . urlencode($this->apiKey);

        // Add record_key if provided for pagination
        if ($recordKey !== null) {
            $url .= '&record_key=' . urlencode($recordKey);
        } else if (!empty($this->recordKey)) {
            // Use default record key if available
            $url .= '&record_key=' . urlencode($this->recordKey);
        }

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Jellyfin-TopUp/1.0');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false || $httpCode != 200) {
            error_log("PayNoi API Error: Failed to fetch data (HTTP $httpCode)");
            return false;
        }

        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('PayNoi API JSON Error: ' . json_last_error_msg());
            return false;
        }

        if (!isset($data['status']) || $data['status'] !== 'success') {
            error_log('PayNoi API Error: ' . ($data['message'] ?? 'Unknown error'));
            return false;
        }

        return $data;
    }
    
    /**
     * Get transactions by amount
     * @param float $amount Transaction amount
     * @param string $startDate Start date
     * @param string $endDate End date
     * @return array
     */
    public function getTransactionsByAmount($amount, $startDate = null, $endDate = null) {
        $allTransactions = $this->getTransactions();

        if (!$allTransactions || !isset($allTransactions['data'])) {
            return [];
        }

        $matchingTransactions = [];

        // Handle new API format: data contains transaction groups
        foreach ($allTransactions['data'] as $transactionGroup) {
            // Each group contains multiple transactions
            foreach ($transactionGroup as $transaction) {
                $transactionAmount = floatval($transaction['amount'] ?? 0);
                $amountDiff = abs($transactionAmount - $amount);

                // Allow tolerance for random decimal (0.01-0.99) and precision
                if ($amountDiff <= 1.00) {
                    // Add date filtering if provided
                    if ($startDate && $endDate) {
                        $transactionTime = strtotime($transaction['date'] ?? '');
                        $startTime = strtotime($startDate);
                        $endTime = strtotime($endDate);

                        if ($transactionTime >= $startTime && $transactionTime <= $endTime) {
                            $matchingTransactions[] = $transaction;
                        }
                    } else {
                        $matchingTransactions[] = $transaction;
                    }
                }
            }
        }

        return $matchingTransactions;
    }
    
    /**
     * Verify transaction matches our payment
     * @param array $transaction PayNoi transaction data
     * @param array $payment Our payment data
     * @return bool
     */
    public function verifyTransaction($transaction, $payment) {
        // Check amount match (allow tolerance for random decimal 0.01-0.99)
        $transactionAmount = floatval($transaction['amount']);
        $paymentAmount = floatval($payment['amount']);
        $amountDiff = abs($transactionAmount - $paymentAmount);

        if ($amountDiff > 1.00) {
            return false;
        }
        
        // Check if transaction is within reasonable time frame
        $transactionDate = $transaction['datetime'] ?? $transaction['created_at'] ?? '';
        $transactionTime = strtotime($transactionDate);
        $paymentTime = strtotime($payment['created_at']);
        $timeDiff = abs($transactionTime - $paymentTime);
        
        // Allow up to 2 hours difference
        if ($timeDiff > 7200) {
            return false;
        }
        
        // Check if transaction is incoming (credit)
        $transactionType = $transaction['type'] ?? '';
        if ($transactionType !== 'credit' && $transactionType !== 'in') {
            return false;
        }
        
        return true;
    }
    
    /**
     * Find matching transaction for payment
     * @param array $payment Our payment data
     * @return array|null
     */
    public function findMatchingTransaction($payment) {
        $startDate = date('Y-m-d H:i:s', strtotime($payment['created_at']) - 7200); // 2 hours before
        $endDate = date('Y-m-d H:i:s', strtotime($payment['created_at']) + 7200); // 2 hours after
        
        $transactions = $this->getTransactionsByAmount($payment['amount'], $startDate, $endDate);
        
        foreach ($transactions as $transaction) {
            if ($this->verifyTransaction($transaction, $payment)) {
                return $transaction;
            }
        }
        
        return null;
    }
    
    /**
     * Get recent transactions (last hour)
     * @return array|false
     */
    public function getRecentTransactions() {
        $startDate = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $endDate = date('Y-m-d H:i:s');

        return $this->getTransactions($startDate, $endDate);
    }

    /**
     * Get transactions by date range for display
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @param int $limit Maximum number of transactions
     * @return array|false
     */
    public function getTransactionsByDateRange($startDate, $endDate, $limit = 100) {
        $startDateTime = $startDate . ' 00:00:00';
        $endDateTime = $endDate . ' 23:59:59';

        // Use existing getTransactions method
        $response = $this->getTransactions();

        if (!$response) {
            return false;
        }

        // Handle PayNoi API response format
        $flatTransactions = [];

        if (isset($response['status']) && $response['status'] === 'success' && isset($response['data'])) {
            // New PayNoi API format: data contains transaction groups
            foreach ($response['data'] as $transactionGroup) {
                // Each group contains multiple transactions
                foreach ($transactionGroup as $transaction) {
                    // Each transaction has: bankaccount, amount, date, balance, type, currency
                    $amount = floatval($transaction['amount'] ?? 0);
                    $balance = floatval($transaction['balance'] ?? 0);
                    $date = $transaction['date'] ?? '';
                    $type = $transaction['type'] ?? '';
                    $bankaccount = $transaction['bankaccount'] ?? '';
                    $currency = $transaction['currency'] ?? 'THB';

                    // Filter by date range
                    $transactionTime = strtotime($date);
                    $startTime = strtotime($startDateTime);
                    $endTime = strtotime($endDateTime);

                    if ($transactionTime < $startTime || $transactionTime > $endTime) {
                        continue; // Skip transactions outside date range
                    }

                    // Determine if it's incoming or outgoing
                    $transaction_type = ($amount > 0) ? 'in' : 'out';

                    // Generate unique ID for transaction
                    $trans_id = md5($bankaccount . $amount . $date . $balance);

                    $flatTransactions[] = [
                        'id' => $trans_id,
                        'datetime' => $date,
                        'timestamp' => $date,
                        'amount' => abs($amount), // Use absolute value for display
                        'balance' => $balance,
                        'type' => $transaction_type,
                        'bankaccount' => $bankaccount,
                        'currency' => $currency,
                        'description' => $type ?: ($transaction_type === 'in' ? 'เงินเข้า' : 'เงินออก'),
                        'memo' => $type,
                        'reference' => $trans_id,
                        'from' => $transaction_type === 'in' ? 'ผู้โอน' : $bankaccount,
                        'to' => $transaction_type === 'out' ? 'ผู้รับ' : $bankaccount,
                        'sender' => $transaction_type === 'in' ? 'ผู้โอน' : $bankaccount,
                        'receiver' => $transaction_type === 'out' ? 'ผู้รับ' : $bankaccount,
                        'raw_data' => $transaction // Keep original data for debugging
                    ];
                }
            }
        }

        // Sort by datetime (newest first)
        usort($flatTransactions, function($a, $b) {
            $timeA = strtotime($a['datetime']);
            $timeB = strtotime($b['datetime']);
            return $timeB - $timeA;
        });

        // Limit results
        if ($limit > 0 && count($flatTransactions) > $limit) {
            $flatTransactions = array_slice($flatTransactions, 0, $limit);
        }

        return $flatTransactions;
    }

    /**
     * Decode Unicode strings from PayNoi API
     */
    private function decodeUnicode($str) {
        if (empty($str)) {
            return '';
        }

        // Decode Unicode escape sequences like \u0e1a\u0e32\u0e17
        $decoded = json_decode('"' . $str . '"');
        return $decoded ?: $str;
    }


}
?>
