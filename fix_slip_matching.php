<?php
/**
 * Fix Slip Matching Issues
 * แก้ไขปัญหาการจับคู่สลิปทันที
 */

require_once 'includes/config.php';
require_once 'includes/PayNoiAPIClass.php';

echo "🔧 Fixing Slip Matching Issues\n";
echo "==============================\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Initialize PayNoi API
    $paynoiAPI = new PayNoiAPI();
    
    echo "1. 📋 Getting pending payments...\n";
    
    // Get pending payments
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, u.phone,
               us.package_id, us.id as subscription_id,
               p.name as package_name, p.price as package_price, p.duration_days
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status IN ('pending_verification', 'pending')
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.created_at DESC
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   Found " . count($pendingPayments) . " pending payments\n\n";
    
    if (count($pendingPayments) == 0) {
        echo "✅ No pending payments to process\n";
        exit(0);
    }
    
    echo "2. 💰 Getting PayNoi transactions...\n";
    
    // Get PayNoi transactions from last 24 hours
    $startDate = date('Y-m-d H:i:s', strtotime('-24 hours'));
    $endDate = date('Y-m-d H:i:s');
    
    $paynoiTransactions = $paynoiAPI->getTransactions($startDate, $endDate);
    
    if (!$paynoiTransactions || !isset($paynoiTransactions['data'])) {
        echo "❌ Failed to fetch PayNoi transactions\n";
        exit(1);
    }
    
    // Flatten PayNoi transactions
    $flatTransactions = [];
    foreach ($paynoiTransactions['data'] as $transactionGroup) {
        foreach ($transactionGroup as $transaction) {
            if (floatval($transaction['amount'] ?? 0) > 0) { // Only incoming transactions
                $flatTransactions[] = $transaction;
            }
        }
    }
    
    echo "   Found " . count($flatTransactions) . " incoming PayNoi transactions\n\n";
    
    if (count($flatTransactions) == 0) {
        echo "❌ No PayNoi transactions found - this is why no matches occurred\n";
        echo "💡 Check:\n";
        echo "   1. PayNoi API credentials\n";
        echo "   2. Recent bank transactions\n";
        echo "   3. API response format\n";
        exit(1);
    }
    
    echo "3. 🔍 Processing matches...\n";
    
    $totalMatched = 0;
    $totalAutoVerified = 0;
    
    foreach ($pendingPayments as $payment) {
        $paymentAmount = floatval($payment['amount']);
        $paymentTime = strtotime($payment['created_at']);
        
        echo "   Processing Payment #{$payment['id']} - {$payment['username']} - {$paymentAmount} THB\n";
        
        $bestMatch = null;
        $bestScore = PHP_FLOAT_MAX;
        
        foreach ($flatTransactions as $transaction) {
            $transactionAmount = floatval($transaction['amount'] ?? 0);
            $transactionTime = strtotime($transaction['date'] ?? '');
            
            // Check amount tolerance (±1.00 THB)
            $amountDiff = abs($transactionAmount - $paymentAmount);
            if ($amountDiff > 1.00) {
                continue;
            }
            
            // Check time tolerance (±2 hours)
            $timeDiff = abs($transactionTime - $paymentTime);
            if ($timeDiff > 7200) { // 2 hours
                continue;
            }
            
            // Calculate match score (lower is better)
            $score = $amountDiff + ($timeDiff / 3600); // Amount diff + time diff in hours
            
            if ($score < $bestScore) {
                $bestScore = $score;
                $bestMatch = [
                    'transaction' => $transaction,
                    'amount_diff' => $amountDiff,
                    'time_diff_minutes' => round($timeDiff / 60),
                    'score' => $score
                ];
            }
        }
        
        if ($bestMatch) {
            $totalMatched++;
            $transaction = $bestMatch['transaction'];
            $transactionTime = date('Y-m-d H:i:s', strtotime($transaction['date']));
            
            echo "      ✅ Found match: {$transaction['amount']} THB at {$transactionTime}\n";
            echo "         Amount diff: {$bestMatch['amount_diff']} THB\n";
            echo "         Time diff: {$bestMatch['time_diff_minutes']} minutes\n";
            
            // Check auto-verification rules
            $autoVerify = false;
            $reason = "";
            
            if ($bestMatch['amount_diff'] <= 0.50 && $bestMatch['time_diff_minutes'] <= 30) {
                $autoVerify = true;
                $reason = "Exact match within 30 minutes";
            } elseif ($paymentAmount <= 100.00 && $bestMatch['amount_diff'] <= 1.00 && $bestMatch['time_diff_minutes'] <= 60) {
                $autoVerify = true;
                $reason = "Small amount with good match";
            } else {
                // Check user history
                $historyStmt = $pdo->prepare("
                    SELECT COUNT(*) as verified_count FROM payment_transactions
                    WHERE user_id = ? AND status IN ('completed', 'verified')
                ");
                $historyStmt->execute([$payment['user_id']]);
                $verifiedCount = $historyStmt->fetch()['verified_count'];
                
                if ($verifiedCount >= 3 && $bestMatch['amount_diff'] <= 1.00 && $bestMatch['time_diff_minutes'] <= 120) {
                    $autoVerify = true;
                    $reason = "Trusted user ({$verifiedCount} previous payments)";
                } else {
                    $reason = "Manual review required";
                }
            }
            
            if ($autoVerify) {
                echo "         🎯 AUTO-VERIFYING: {$reason}\n";
                
                // Update payment status
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions 
                    SET status = 'verified', 
                        verified_at = NOW(),
                        admin_notes = ?,
                        paynoi_transaction_id = ?
                    WHERE id = ?
                ");
                $adminNotes = "Auto-verified: {$reason}. PayNoi match: {$transaction['amount']} THB, diff: {$bestMatch['amount_diff']} THB, time: {$bestMatch['time_diff_minutes']}min";
                $updateStmt->execute([
                    $adminNotes,
                    $transaction['id'] ?? $transaction['trans_id'] ?? '',
                    $payment['id']
                ]);
                
                // Enable Jellyfin user if needed
                if (!empty($payment['jellyfin_user_id'])) {
                    require_once 'includes/JellyfinAPI.php';
                    $jellyfinAPI = new JellyfinAPI();
                    $jellyfinAPI->enableUser($payment['jellyfin_user_id']);
                    echo "         🎬 Enabled Jellyfin user\n";
                }
                
                // Update subscription dates
                if ($payment['subscription_id']) {
                    $subscriptionStmt = $pdo->prepare("
                        UPDATE user_subscriptions 
                        SET start_date = NOW(), 
                            end_date = DATE_ADD(NOW(), INTERVAL ? DAY),
                            status = 'active'
                        WHERE id = ?
                    ");
                    $subscriptionStmt->execute([
                        $payment['duration_days'] ?? 30,
                        $payment['subscription_id']
                    ]);
                    echo "         📅 Updated subscription\n";
                }
                
                // Award affiliate points if referred
                $referrerStmt = $pdo->prepare("SELECT referred_by FROM users WHERE id = ?");
                $referrerStmt->execute([$payment['user_id']]);
                $referrer = $referrerStmt->fetch();
                
                if ($referrer && $referrer['referred_by']) {
                    $pointsStmt = $pdo->prepare("
                        UPDATE users 
                        SET affiliate_points = affiliate_points + 15 
                        WHERE id = ?
                    ");
                    $pointsStmt->execute([$referrer['referred_by']]);
                    
                    // Log referral transaction
                    $refLogStmt = $pdo->prepare("
                        INSERT INTO referral_transactions (referrer_id, referred_user_id, points_awarded, transaction_type, created_at)
                        VALUES (?, ?, 15, 'package_purchase', NOW())
                    ");
                    $refLogStmt->execute([$referrer['referred_by'], $payment['user_id']]);
                    echo "         🎁 Awarded 15 affiliate points\n";
                }
                
                $totalAutoVerified++;
                
            } else {
                echo "         ⚠️  MANUAL REVIEW: {$reason}\n";
                
                // Update to manual review status
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions 
                    SET status = 'manual_review',
                        admin_notes = ?
                    WHERE id = ?
                ");
                $adminNotes = "Manual review needed: {$reason}. PayNoi match: {$transaction['amount']} THB, diff: {$bestMatch['amount_diff']} THB, time: {$bestMatch['time_diff_minutes']}min";
                $updateStmt->execute([$adminNotes, $payment['id']]);
            }
            
        } else {
            echo "      ❌ No matching PayNoi transaction found\n";
        }
        
        echo "\n";
    }
    
    echo "4. 📊 Summary:\n";
    echo "==============\n";
    echo "Total pending payments: " . count($pendingPayments) . "\n";
    echo "Total matched: {$totalMatched}\n";
    echo "Total auto-verified: {$totalAutoVerified}\n";
    echo "Manual review needed: " . ($totalMatched - $totalAutoVerified) . "\n";
    echo "\n";
    
    if ($totalAutoVerified > 0) {
        echo "✅ Successfully auto-verified {$totalAutoVerified} payments!\n";
        echo "🎬 Users should now be able to access Jellyfin\n";
    }
    
    if ($totalMatched < count($pendingPayments)) {
        echo "⚠️  Some payments couldn't be matched automatically\n";
        echo "💡 Possible reasons:\n";
        echo "   - PayNoi transactions not yet available\n";
        echo "   - Amount or time differences too large\n";
        echo "   - Need to adjust tolerance settings\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
