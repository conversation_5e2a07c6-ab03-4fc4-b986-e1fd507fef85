# PayNoi Transaction Check - Every 5 seconds
# Jellyfin by James - Production Cron Configuration
# 
# Installation:
# sudo crontab -u www-data paynoi_crontab_5sec.txt
# 
# Or manually add these lines to crontab:
# sudo crontab -u www-data -e

# PayNoi payment verification - every 5 seconds (12 times per minute)
* * * * * /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1

# Manual payment slip verification - every 5 seconds (12 times per minute)
* * * * * /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1

# User expiration check - every 1 minute
* * * * * /usr/bin/php /var/www/html/check_expired_users.php >> /var/log/jellyfin/expiration_cron.log 2>&1

# System health check - every 15 minutes
*/15 * * * * /usr/bin/php /var/www/html/cron/linux_health_check.php >> /var/log/jellyfin/health_cron.log 2>&1

# Log cleanup - daily at 2 AM
0 2 * * * find /var/log/jellyfin -name "*.log" -size +100M -exec truncate -s 10M {} \; >> /var/log/jellyfin/cleanup.log 2>&1
