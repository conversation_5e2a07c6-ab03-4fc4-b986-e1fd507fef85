# Jellyfin by James - Complete Automated Cron Configuration
# Production Cron Jobs for Full Payment & User Management Automation
#
# Installation:
# sudo crontab -u www-data paynoi_crontab_5sec.txt
#
# Or manually add these lines to crontab:
# sudo crontab -u www-data -e
#
# This configuration includes:
# 1. PayNoi transaction verification (every 5 seconds)
# 2. Manual slip verification and matching (every 5 seconds)
# 3. User expiration checking and Jellyfin disable (every 1 minute)
# 4. System health monitoring (every 15 minutes)
# 5. Log cleanup and maintenance

# ============================================
# PAYNOI TRANSACTION VERIFICATION - EVERY 5 SECONDS
# ============================================
# Fetches PayNoi transactions and auto-verifies payments
* * * * * /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1

# ============================================
# MANUAL SLIP VERIFICATION - EVERY 5 SECONDS
# ============================================
# Checks manual payment slips and matches with PayNoi amounts for approval
* * * * * /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1

# ============================================
# USER EXPIRATION CHECK - EVERY 1 MINUTE
# ============================================
# Checks for expired users and disables them in Jellyfin
* * * * * /usr/bin/php /var/www/html/cron/check_expiration.php >> /var/log/jellyfin/expiration_cron.log 2>&1

# ============================================
# SYSTEM HEALTH CHECK - EVERY 15 MINUTES
# ============================================
# Monitors system health, database, and API connectivity
*/15 * * * * /usr/bin/php /var/www/html/cron/linux_health_check.php >> /var/log/jellyfin/health_cron.log 2>&1

# ============================================
# LOG CLEANUP - DAILY AT 2 AM
# ============================================
# Prevents log files from growing too large
0 2 * * * find /var/log/jellyfin -name "*.log" -size +100M -exec truncate -s 10M {} \; >> /var/log/jellyfin/cleanup.log 2>&1

# ============================================
# WEEKLY MAINTENANCE - SUNDAY AT 3 AM
# ============================================
# Database optimization and system maintenance
0 3 * * 0 /usr/bin/php /var/www/html/cron/linux_health_check.php >> /var/log/jellyfin/weekly_maintenance.log 2>&1
