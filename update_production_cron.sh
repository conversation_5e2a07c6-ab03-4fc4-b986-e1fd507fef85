#!/bin/bash

# Update Production Server with Fixed Cron Scripts
# This script uploads and applies all the fixes to the production server

echo "🎬 Jellyfin by James - Production Cron Update"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PRODUCTION_SERVER="*************"
PRODUCTION_USER="user"
PRODUCTION_PATH="/var/www/html"
LOCAL_PATH="."

echo -e "${BLUE}📋 Production Server: $PRODUCTION_SERVER${NC}"
echo -e "${BLUE}📁 Target Path: $PRODUCTION_PATH${NC}"
echo ""

# Check if we can connect to production server
echo -e "${BLUE}🔗 Testing connection to production server...${NC}"
if ! ssh -o ConnectTimeout=5 "$PRODUCTION_USER@$PRODUCTION_SERVER" "echo 'Connection successful'" 2>/dev/null; then
    echo -e "${RED}❌ Cannot connect to production server${NC}"
    echo "Please check:"
    echo "1. SSH connection to $PRODUCTION_USER@$PRODUCTION_SERVER"
    echo "2. SSH keys are properly configured"
    exit 1
fi
echo -e "${GREEN}✅ Connection successful${NC}"

# Upload fixed files
echo ""
echo -e "${BLUE}📤 Uploading fixed files to production...${NC}"

# Core cron scripts
echo -e "${YELLOW}Uploading paynoi-transactions.php...${NC}"
scp "$LOCAL_PATH/paynoi-transactions.php" "$PRODUCTION_USER@$PRODUCTION_SERVER:$PRODUCTION_PATH/"

echo -e "${YELLOW}Uploading cron/check_expiration.php...${NC}"
scp "$LOCAL_PATH/cron/check_expiration.php" "$PRODUCTION_USER@$PRODUCTION_SERVER:$PRODUCTION_PATH/cron/"

# Database setup scripts
echo -e "${YELLOW}Uploading setup_cron_database.php...${NC}"
scp "$LOCAL_PATH/setup_cron_database.php" "$PRODUCTION_USER@$PRODUCTION_SERVER:$PRODUCTION_PATH/"

echo -e "${YELLOW}Uploading fix_cron_database.sh...${NC}"
scp "$LOCAL_PATH/fix_cron_database.sh" "$PRODUCTION_USER@$PRODUCTION_SERVER:$PRODUCTION_PATH/"

# Updated setup scripts
echo -e "${YELLOW}Uploading setup_paynoi_cron_5sec.sh...${NC}"
scp "$LOCAL_PATH/setup_paynoi_cron_5sec.sh" "$PRODUCTION_USER@$PRODUCTION_SERVER:$PRODUCTION_PATH/"

# Documentation
echo -e "${YELLOW}Uploading documentation...${NC}"
scp "$LOCAL_PATH/INSTALL_PAYNOI_CRON_5SEC.md" "$PRODUCTION_USER@$PRODUCTION_SERVER:$PRODUCTION_PATH/"
scp "$LOCAL_PATH/CRON_FIXES_SUMMARY.md" "$PRODUCTION_USER@$PRODUCTION_SERVER:$PRODUCTION_PATH/"

echo -e "${GREEN}✅ All files uploaded successfully${NC}"

# Run database fix on production
echo ""
echo -e "${BLUE}🔧 Running database fix on production server...${NC}"
ssh "$PRODUCTION_USER@$PRODUCTION_SERVER" << 'EOF'
cd /var/www/html

# Make scripts executable
chmod +x fix_cron_database.sh
chmod +x setup_paynoi_cron_5sec.sh

echo "🗄️  Running database setup..."
sudo php setup_cron_database.php

echo ""
echo "🧪 Testing fixed scripts..."

echo "Testing PayNoi transactions script..."
sudo -u www-data php paynoi-transactions.php 2>&1 | head -5

echo ""
echo "Testing expiration check script..."
sudo -u www-data php cron/check_expiration.php 2>&1 | head -5

echo ""
echo "✅ Production update completed!"
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Production server updated successfully${NC}"
else
    echo -e "${RED}❌ Production update failed${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🎯 Next Steps:${NC}"
echo "1. SSH to production: ssh $PRODUCTION_USER@$PRODUCTION_SERVER"
echo "2. Run full cron setup: sudo ./setup_paynoi_cron_5sec.sh"
echo "3. Monitor logs: sudo tail -f /var/log/jellyfin/*.log"
echo ""
echo -e "${GREEN}🎉 Production server is now ready for automated cron jobs!${NC}"
