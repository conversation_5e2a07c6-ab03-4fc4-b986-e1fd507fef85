<?php
/**
 * Test Slip Matching Right Now
 * ทดสอบการจับคู่สลิปในขณะนี้
 */

echo "🧪 Testing Slip Matching System\n";
echo "===============================\n\n";

// Test 1: Check PayNoi API
echo "1. 🔍 Testing PayNoi API...\n";
system("php check_paynoi_data.php");

echo "\n" . str_repeat("=", 50) . "\n\n";

// Test 2: Debug slip matching
echo "2. 🔍 Debugging slip matching...\n";
system("php debug_slip_matching.php");

echo "\n" . str_repeat("=", 50) . "\n\n";

// Test 3: Test manual check payments (current system)
echo "3. 🔍 Testing current manual_check_payments.php...\n";
system("php manual_check_payments.php");

echo "\n" . str_repeat("=", 50) . "\n\n";

// Test 4: Test new auto slip verification
echo "4. 🔍 Testing new auto_slip_verification.php...\n";
if (file_exists('auto_slip_verification.php')) {
    system("php auto_slip_verification.php");
} else {
    echo "❌ auto_slip_verification.php not found\n";
}

echo "\n" . str_repeat("=", 50) . "\n\n";

echo "🎯 Testing Complete!\n";
echo "Check the results above to identify the issue.\n";
?>
