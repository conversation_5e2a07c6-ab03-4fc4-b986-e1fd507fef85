<?php
/**
 * Fix payments table by adding package_id column
 * This fixes the SQL error: Unknown column 'py.package_id' in 'ON'
 */

require_once 'config/database.php';

try {
    echo "Starting payments table fix...\n";
    
    // Check if package_id column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM payments LIKE 'package_id'");
    $columnExists = $stmt->rowCount() > 0;
    
    if ($columnExists) {
        echo "✅ package_id column already exists in payments table\n";
    } else {
        echo "➕ Adding package_id column to payments table...\n";
        
        // Add package_id column to payments table
        $pdo->exec("ALTER TABLE payments ADD COLUMN package_id INT NULL AFTER amount");
        echo "✅ Added package_id column\n";
        
        // Add index for better performance
        $pdo->exec("ALTER TABLE payments ADD INDEX idx_package_id (package_id)");
        echo "✅ Added index for package_id column\n";
    }
    
    // Show updated table structure
    echo "\n📋 Current payments table structure:\n";
    $stmt = $pdo->query("DESCRIBE payments");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        $nullable = $column['Null'] === 'YES' ? 'NULL' : 'NOT NULL';
        $default = $column['Default'] ? " DEFAULT '{$column['Default']}'" : '';
        echo "   {$column['Field']} - {$column['Type']} {$nullable}{$default}\n";
    }
    
    // Check current payments count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM payments");
    $count = $stmt->fetch()['count'];
    echo "\n📊 Current payments count: {$count}\n";
    
    echo "\n✅ Database update completed successfully!\n";
    echo "The manual payment check should now work without errors.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
