<?php
/**
 * Test Auto Slip Verification Script
 * ทดสอบการทำงานของระบบตรวจสอบสลิปอัตโนมัติ
 */

require_once 'includes/config.php';

echo "🧪 Testing Auto Slip Verification System\n";
echo "========================================\n\n";

// Test 1: Check database connection
echo "1. Testing database connection...\n";
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Check required tables
echo "2. Checking required tables...\n";
$requiredTables = ['payment_transactions', 'users', 'user_subscriptions', 'packages'];
foreach ($requiredTables as $table) {
    $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Table '{$table}' exists\n";
    } else {
        echo "❌ Table '{$table}' missing\n";
    }
}
echo "\n";

// Test 3: Check PayNoi API class
echo "3. Testing PayNoi API class...\n";
if (file_exists('includes/PayNoiAPIClass.php')) {
    require_once 'includes/PayNoiAPIClass.php';
    try {
        $paynoiAPI = new PayNoiAPI();
        echo "✅ PayNoi API class loaded successfully\n";
    } catch (Exception $e) {
        echo "❌ PayNoi API class error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ PayNoi API class file not found\n";
}
echo "\n";

// Test 4: Check Jellyfin API class
echo "4. Testing Jellyfin API class...\n";
if (file_exists('includes/JellyfinAPI.php')) {
    require_once 'includes/JellyfinAPI.php';
    try {
        $jellyfin = new JellyfinAPI();
        echo "✅ Jellyfin API class loaded successfully\n";
    } catch (Exception $e) {
        echo "❌ Jellyfin API class error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Jellyfin API class file not found\n";
}
echo "\n";

// Test 5: Check pending payments
echo "5. Checking pending payments with slips...\n";
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM payment_transactions
        WHERE status IN ('pending_verification', 'pending')
        AND slip_image IS NOT NULL
        AND slip_image != ''
        AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $stmt->execute();
    $pendingCount = $stmt->fetch()['count'];
    echo "📋 Found {$pendingCount} pending payments with slips (last 24 hours)\n";
    
    if ($pendingCount > 0) {
        // Show sample pending payments
        $stmt = $pdo->prepare("
            SELECT pt.id, pt.amount, pt.created_at, pt.status, u.username
            FROM payment_transactions pt
            JOIN users u ON pt.user_id = u.id
            WHERE pt.status IN ('pending_verification', 'pending')
            AND pt.slip_image IS NOT NULL
            AND pt.slip_image != ''
            AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY pt.created_at DESC
            LIMIT 5
        ");
        $stmt->execute();
        $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "📋 Sample pending payments:\n";
        foreach ($samples as $sample) {
            echo "   - ID {$sample['id']}: {$sample['username']} - {$sample['amount']} THB - {$sample['created_at']} ({$sample['status']})\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking pending payments: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 6: Test PayNoi API connection
echo "6. Testing PayNoi API connection...\n";
if (isset($paynoiAPI)) {
    try {
        $startDate = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $endDate = date('Y-m-d H:i:s');
        
        echo "   Fetching transactions from {$startDate} to {$endDate}...\n";
        $transactions = $paynoiAPI->getTransactions($startDate, $endDate);
        
        if ($transactions && isset($transactions['data'])) {
            $transactionCount = 0;
            foreach ($transactions['data'] as $group) {
                $transactionCount += count($group);
            }
            echo "✅ PayNoi API working - Found {$transactionCount} transactions\n";
        } else {
            echo "⚠️  PayNoi API returned no data or invalid format\n";
        }
    } catch (Exception $e) {
        echo "❌ PayNoi API error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ PayNoi API not available for testing\n";
}
echo "\n";

// Test 7: Test Jellyfin API connection
echo "7. Testing Jellyfin API connection...\n";
if (isset($jellyfin)) {
    try {
        $users = $jellyfin->getUsers();
        if ($users && is_array($users)) {
            $userCount = count($users);
            echo "✅ Jellyfin API working - Found {$userCount} users\n";
        } else {
            echo "⚠️  Jellyfin API returned no users or invalid format\n";
        }
    } catch (Exception $e) {
        echo "❌ Jellyfin API error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Jellyfin API not available for testing\n";
}
echo "\n";

// Test 8: Check auto_slip_verification.php file
echo "8. Checking auto slip verification script...\n";
if (file_exists('auto_slip_verification.php')) {
    echo "✅ auto_slip_verification.php exists\n";
    
    // Check if it's executable
    if (is_readable('auto_slip_verification.php')) {
        echo "✅ Script is readable\n";
    } else {
        echo "❌ Script is not readable\n";
    }
} else {
    echo "❌ auto_slip_verification.php not found\n";
}
echo "\n";

// Test 9: Simulate running the script (dry run)
echo "9. Simulating auto slip verification (dry run)...\n";
if (file_exists('auto_slip_verification.php') && isset($pdo)) {
    try {
        // Get pending payments count before
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM payment_transactions
            WHERE status IN ('pending_verification', 'pending')
            AND slip_image IS NOT NULL
            AND slip_image != ''
        ");
        $stmt->execute();
        $beforeCount = $stmt->fetch()['count'];
        
        echo "   Pending payments before: {$beforeCount}\n";
        echo "   ℹ️  To run actual verification, execute: php auto_slip_verification.php\n";
        
    } catch (Exception $e) {
        echo "❌ Simulation error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Cannot simulate - missing files or database\n";
}
echo "\n";

// Test 10: Check log directory permissions
echo "10. Checking log directory...\n";
$logDir = '/var/log/jellyfin';
if (is_dir($logDir)) {
    echo "✅ Log directory exists: {$logDir}\n";
    if (is_writable($logDir)) {
        echo "✅ Log directory is writable\n";
    } else {
        echo "⚠️  Log directory is not writable - may need sudo permissions\n";
    }
} else {
    echo "⚠️  Log directory does not exist: {$logDir}\n";
    echo "   Create with: sudo mkdir -p {$logDir} && sudo chown www-data:www-data {$logDir}\n";
}
echo "\n";

echo "🎯 Test Summary:\n";
echo "================\n";
echo "✅ If all tests pass, the auto slip verification system is ready\n";
echo "🚀 To start using it:\n";
echo "   1. Upload auto_slip_verification.php to production server\n";
echo "   2. Run: sudo ./setup_paynoi_cron_5sec.sh\n";
echo "   3. Monitor logs: sudo tail -f /var/log/jellyfin/slip_verification_cron.log\n";
echo "\n";
echo "📊 Expected behavior:\n";
echo "   - Checks pending slips every 5 seconds\n";
echo "   - Matches with PayNoi transactions by amount and time\n";
echo "   - Auto-verifies trusted users and exact matches\n";
echo "   - Marks questionable payments for manual review\n";
echo "   - Enables Jellyfin users and awards affiliate points\n";
echo "\n";
echo "🎬 Jellyfin by James - Auto Slip Verification Test Complete!\n";
?>
