<?php
/**
 * PayNoi Transactions Sync Script
 * This script fetches transactions from PayNoi API and processes pending payments
 * Run via cron job every 5 seconds
 */

require_once 'includes/config.php';
require_once 'includes/PayNoiAPIClass.php';

// Set execution time limit
set_time_limit(30);

// Log start of execution
error_log("PayNoi Transactions Sync - Started at " . date('Y-m-d H:i:s'));

try {
    // Initialize PayNoi API with config values
    $paynoiAPI = new PayNoiAPI(PAYNOI_API_KEY, PAYNOI_RECORD_KEY);

    // Check if API is configured
    if (!$paynoiAPI->isConfigured()) {
        error_log("PayNoi API not configured properly");
        exit(1);
    }
    
    // Get recent transactions from PayNoi
    $transactions = $paynoiAPI->getTransactions();
    
    if (!$transactions || !isset($transactions['data'])) {
        error_log("Failed to fetch transactions from PayNoi API");
        exit(1);
    }
    
    $transactionCount = count($transactions['data']);
    $message = "Fetched {$transactionCount} transactions from PayNoi API";
    error_log($message);
    echo $message . "\n";
    
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if payment_transactions table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'payment_transactions'")->rowCount();
    if ($tableCheck == 0) {
        $message = "payment_transactions table not found. Please run database setup first.";
        error_log($message);
        echo $message . "\n";

        $message = "PayNoi Transactions Sync - Completed: 0 payments verified (no payment_transactions table)";
        error_log($message);
        echo $message . "\n";
        exit(0);
    }

    // Get all pending payments from payment_transactions table with user referral info
    $stmt = $pdo->prepare("
        SELECT pt.*, us.package_id, p.duration_days, p.max_simultaneous_sessions,
               ju.jellyfin_user_id, ju.jellyfin_username,
               u.referred_by, u.username as user_username,
               r.username as referrer_username
        FROM payment_transactions pt
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        LEFT JOIN users u ON pt.user_id = u.id
        LEFT JOIN users r ON u.referred_by = r.id
        LEFT JOIN jellyfin_users ju ON u.id = ju.user_id
        WHERE pt.status = 'pending'
        ORDER BY pt.created_at DESC
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $pendingCount = count($pendingPayments);
    $message = "Found {$pendingCount} pending payments to verify";
    error_log($message);
    echo $message . "\n";
    
    $verifiedCount = 0;
    $enabledUsers = 0;
    
    // Process each pending payment
    foreach ($pendingPayments as $payment) {
        $paymentAmount = floatval($payment['amount']);
        $paymentTime = strtotime($payment['created_at']);
        
        // Look for matching transaction in new API format
        foreach ($transactions['data'] as $transactionGroup) {
            foreach ($transactionGroup as $transaction) {
                $transactionAmount = floatval($transaction['amount'] ?? 0);
                $transactionDate = $transaction['date'] ?? '';
                $transactionTime = strtotime($transactionDate);

                // Skip if not incoming transaction
                if ($transactionAmount <= 0) {
                    continue;
                }

                // Check amount match (within 1.00 tolerance for random decimal 0.01-0.99)
                $amountDiff = abs($transactionAmount - $paymentAmount);
                if ($amountDiff > 1.00) {
                    continue;
                }

                // Check time match (within 2 hours)
                $timeDiff = abs($transactionTime - $paymentTime);
                if ($timeDiff > 7200) { // 2 hours
                    continue;
                }
            
            // Transaction matches! Update payment_transactions status
            $updateStmt = $pdo->prepare("
                UPDATE payment_transactions
                SET status = 'completed',
                    transaction_ref = ?,
                    paid_at = NOW(),
                    updated_at = NOW()
                WHERE id = ?
            ");

            $updateStmt->execute([
                $transaction['trans_id'] ?? '',
                $payment['id']
            ]);
            
            $verifiedCount++;
            error_log("Verified payment ID {$payment['id']} with transaction {$transaction['trans_id']}");
            
            // Enable user in Jellyfin if this is their first payment
            if ($payment['user_id']) {
                // User info is already in the payment data from JOIN
                
                // Determine package based on payment amount
                $packageStmt = $pdo->prepare("
                    SELECT id, max_simultaneous_sessions, duration_days
                    FROM packages
                    WHERE price = ? AND is_active = 1
                    ORDER BY id ASC
                    LIMIT 1
                ");
                $packageStmt->execute([$paymentAmount]);
                $package = $packageStmt->fetch(PDO::FETCH_ASSOC);

                if ($payment['package_id']) {
                    try {
                        // Update user subscription status
                        $subscriptionStmt = $pdo->prepare("
                            UPDATE user_subscriptions
                            SET status = 'active',
                                start_date = NOW(),
                                end_date = DATE_ADD(NOW(), INTERVAL ? DAY),
                                updated_at = NOW()
                            WHERE id = ?
                        ");
                        $subscriptionStmt->execute([
                            $payment['duration_days'] ?? 30,
                            $payment['subscription_id']
                        ]);

                        // Try to enable Jellyfin user if they have an account
                        if (isset($payment['jellyfin_user_id']) && $payment['jellyfin_user_id']) {
                            try {
                                require_once 'includes/JellyfinAPI.php';
                                $jellyfin = new JellyfinAPI();

                                // Get max sessions from package or use default
                                $maxSessions = $payment['max_simultaneous_sessions'] ?? ($package['max_simultaneous_sessions'] ?? 1);

                                // Enable user and grant access to all libraries with package max sessions
                                $result = $jellyfin->enableAllLibraries($payment['jellyfin_user_id'], $maxSessions);

                                if ($result) {
                                    // Update database record
                                    $stmt = $pdo->prepare("UPDATE jellyfin_users SET max_simultaneous_sessions = ? WHERE jellyfin_user_id = ?");
                                    $stmt->execute([$maxSessions, $payment['jellyfin_user_id']]);

                                    $enabledUsers++;
                                    error_log("Enabled Jellyfin user {$payment['jellyfin_user_id']} with {$maxSessions} max sessions for payment {$payment['id']}");
                                } else {
                                    error_log("Failed to enable Jellyfin user {$payment['jellyfin_user_id']} for payment {$payment['id']}");
                                }
                            } catch (Exception $e) {
                                error_log("Jellyfin API error for payment {$payment['id']}: " . $e->getMessage());
                            }
                        } else {
                            error_log("No Jellyfin user ID found for user {$payment['user_id']} in payment {$payment['id']}");
                        }

                        // Award referral points if user was referred
                        if (isset($payment['referred_by']) && $payment['referred_by']) {
                            try {
                                // Check if points already awarded for this payment
                                $checkStmt = $pdo->prepare("
                                    SELECT id FROM referral_transactions
                                    WHERE related_payment_id = ? AND transaction_type = 'purchase'
                                ");
                                $checkStmt->execute([$payment['id']]);

                                if ($checkStmt->rowCount() == 0) {
                                    // Award 15 points to referrer
                                    $pointsStmt = $pdo->prepare("
                                        UPDATE users
                                        SET affiliate_points = COALESCE(affiliate_points, 0) + 15
                                        WHERE id = ?
                                    ");
                                    $pointsStmt->execute([$payment['referred_by']]);

                                    // Log the referral transaction
                                    $logStmt = $pdo->prepare("
                                        INSERT INTO referral_transactions
                                        (referrer_id, referred_id, points_earned, transaction_type, related_payment_id, description, created_at)
                                        VALUES (?, ?, 15, 'purchase', ?, ?, NOW())
                                    ");
                                    $description = "Referral bonus: {$payment['user_username']} purchased package (Amount: {$payment['amount']} THB)";
                                    $logStmt->execute([
                                        $payment['referred_by'],
                                        $payment['user_id'],
                                        $payment['id'],
                                        $description
                                    ]);

                                    error_log("Awarded 15 affiliate points to {$payment['referrer_username']} (ID: {$payment['referred_by']}) for referral payment {$payment['id']} by {$payment['user_username']}");
                                } else {
                                    error_log("Affiliate points already awarded for payment {$payment['id']}");
                                }
                            } catch (Exception $e) {
                                error_log("Failed to award affiliate points for payment {$payment['id']}: " . $e->getMessage());
                            }
                        } else {
                            error_log("No referrer found for user {$payment['user_username']} (ID: {$payment['user_id']}) in payment {$payment['id']}");
                        }
                    } catch (Exception $e) {
                        error_log("Failed to enable Jellyfin user: " . $e->getMessage());
                    }
                }
                
                // Add subscription days to user
                $days = calculateSubscriptionDays($paymentAmount);
                if ($days > 0) {
                    $addDaysStmt = $pdo->prepare("
                        UPDATE users 
                        SET subscription_end = CASE 
                            WHEN subscription_end > NOW() THEN DATE_ADD(subscription_end, INTERVAL ? DAY)
                            ELSE DATE_ADD(NOW(), INTERVAL ? DAY)
                        END
                        WHERE id = ?
                    ");
                    $addDaysStmt->execute([$days, $days, $payment['user_id']]);
                    
                    error_log("Added {$days} subscription days to user ID {$payment['user_id']}");
                }
                
                // Award affiliate points if applicable
                if ($payment['affiliate_user_id']) {
                    $pointsStmt = $pdo->prepare("
                        UPDATE users 
                        SET affiliate_points = affiliate_points + ? 
                        WHERE id = ?
                    ");
                    $affiliatePoints = calculateAffiliatePoints($paymentAmount);
                    $pointsStmt->execute([$affiliatePoints, $payment['affiliate_user_id']]);
                    
                    error_log("Awarded {$affiliatePoints} affiliate points to user ID {$payment['affiliate_user_id']}");
                }
            }

            break; // Found match, stop looking for this payment
        }
    }
    }
    
    $message = "PayNoi Transactions Sync - Completed: {$verifiedCount} payments verified, {$enabledUsers} users enabled";
    error_log($message);
    echo $message . "\n";
    
} catch (Exception $e) {
    error_log("PayNoi Transactions Sync Error: " . $e->getMessage());
    exit(1);
}

/**
 * Enable user in Jellyfin
 */
function enableJellyfinUser($jellyfinUserId) {
    // This would integrate with Jellyfin API
    // For now, return true as placeholder
    return true;
}

/**
 * Calculate subscription days based on payment amount
 */
function calculateSubscriptionDays($amount) {
    // Example pricing: 100 THB = 30 days
    return floor($amount / 100) * 30;
}

/**
 * Calculate affiliate points based on payment amount
 */
function calculateAffiliatePoints($amount) {
    // Example: 5% of payment amount as points
    return floor($amount * 0.05);
}
?>
