# 🎬 PayNoi Cron Setup - Every 5 seconds

## 🚀 **การติดตั้ง Cron Jobs สำหรับ PayNoi ทุก 5 วินาที**

### 📋 **วิธีการติดตั้ง (เลือก 1 วิธี):**

---

## **วิธีที่ 1: ใช้ Script อัตโนมัติ (แนะนำ)**

```bash
# 1. ให้สิทธิ์ execute
chmod +x setup_paynoi_cron_5sec.sh

# 2. รันการติดตั้ง
sudo ./setup_paynoi_cron_5sec.sh
```

---

## **วิธีที่ 2: ติดตั้งด้วยไฟล์ crontab**

```bash
# 1. สร้าง log directory
sudo mkdir -p /var/log/jellyfin
sudo chown www-data:www-data /var/log/jellyfin
sudo chmod 755 /var/log/jellyfin

# 2. ติดตั้ง crontab
sudo crontab -u www-data paynoi_crontab_5sec.txt
```

---

## **วิธีที่ 3: ติดตั้งแบบ Manual**

```bash
# 1. เปิด crontab editor
sudo crontab -u www-data -e

# 2. เพิ่มบรรทัดเหล่านี้:
```

```cron
# PayNoi payment verification - every 5 seconds
* * * * * /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1

# Manual payment slip verification - every 5 seconds
* * * * * /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1

# User expiration check - every 1 minute
* * * * * /usr/bin/php /var/www/html/check_expired_users.php >> /var/log/jellyfin/expiration_cron.log 2>&1
```

---

## ⏰ **ตารางการทำงาน**

| **งาน** | **ความถี่** | **จำนวนครั้งต่อนาที** | **จำนวนครั้งต่อชั่วโมง** |
|---------|-------------|----------------------|------------------------|
| **PayNoi Transaction Check** | ทุก 5 วินาที | 12 ครั้ง | 720 ครั้ง |
| **Manual Payment Check** | ทุก 5 วินาที | 12 ครั้ง | 720 ครั้ง |
| **User Expiration Check** | ทุก 1 นาที | 1 ครั้ง | 60 ครั้ง |

---

## 📊 **การตรวจสอบและ Monitoring**

### **ตรวจสอบสถานะ:**
```bash
# ดู cron jobs ที่ติดตั้งแล้ว
sudo crontab -u www-data -l

# ตรวจสอบสถานะ cron service
sudo systemctl status cron

# ดู log files แบบ real-time
sudo tail -f /var/log/jellyfin/payment_cron.log
sudo tail -f /var/log/jellyfin/manual_cron.log
sudo tail -f /var/log/jellyfin/expiration_cron.log
```

### **ตรวจสอบการทำงาน:**
```bash
# ดู log entries ล่าสุด
sudo tail -n 50 /var/log/jellyfin/payment_cron.log

# ตรวจสอบ cron system logs
sudo journalctl -u cron -f

# ดูสถิติการทำงาน
sudo grep "PayNoi Transactions Sync - Completed" /var/log/jellyfin/payment_cron.log | tail -10
```

---

## 🔧 **การแก้ไขปัญหา**

### **ปัญหาที่พบบ่อย:**

#### **1. Cron Jobs ไม่ทำงาน**
```bash
# ตรวจสอบ cron service
sudo systemctl restart cron
sudo systemctl status cron

# ตรวจสอบ permissions
sudo chown www-data:www-data /var/log/jellyfin
sudo chmod 755 /var/log/jellyfin
```

#### **2. PHP Script Error**
```bash
# ทดสอบ script แบบ manual
sudo -u www-data php /var/www/html/paynoi-transactions.php
sudo -u www-data php /var/www/html/manual_check_payments.php
```

#### **3. Log Files ใหญ่เกินไป**
```bash
# ลบ log files เก่า
sudo truncate -s 0 /var/log/jellyfin/payment_cron.log
sudo truncate -s 0 /var/log/jellyfin/manual_cron.log
```

---

## ⚠️ **ข้อควรระวัง**

### **Performance Impact:**
- **CPU Usage**: จะมีการใช้ CPU สูงขึ้นเนื่องจากรันทุก 5 วินาที
- **Database Load**: จะมี database queries มากขึ้น (720 ครั้งต่อชั่วโมง)
- **Log Files**: Log files จะเติบโตเร็วมาก

### **Recommendations:**
- **Monitor Server Resources**: ตรวจสอบ CPU และ Memory usage
- **Database Optimization**: ใช้ indexes ที่เหมาะสม
- **Log Rotation**: ตั้งค่า log rotation เพื่อป้องกัน disk full

---

## ✅ **การยืนยันการติดตั้งสำเร็จ**

### **ตรวจสอบว่าระบบทำงานปกติ:**
```bash
# 1. ดู cron jobs
sudo crontab -u www-data -l | grep paynoi-transactions

# 2. ตรวจสอบ log files มีการอัปเดต
ls -la /var/log/jellyfin/

# 3. ดู log entries ล่าสุด
sudo tail -f /var/log/jellyfin/payment_cron.log

# 4. ควรเห็น log entries ใหม่ทุก 5 วินาที
```

---

## 🎯 **Success Criteria**

**ระบบทำงานถูกต้องเมื่อ:**
- ✅ เห็น log entries ใหม่ทุก 5 วินาทีในไฟล์ `/var/log/jellyfin/payment_cron.log`
- ✅ PayNoi transactions ถูกตรวจสอบและ verify อัตโนมัติ
- ✅ Manual payments ถูกตรวจสอบและ verify อัตโนมัติ
- ✅ User expiration ถูกตรวจสอบทุกนาที
- ✅ ไม่มี error messages ใน log files

**🚀 ระบบพร้อมใช้งานแล้ว! PayNoi transactions จะถูกตรวจสอบทุก 5 วินาที**
