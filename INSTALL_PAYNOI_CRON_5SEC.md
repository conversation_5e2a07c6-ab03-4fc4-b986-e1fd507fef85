# 🎬 Jellyfin by James - Complete Automation Setup

## 🚀 **การติดตั้ง Cron Jobs แบบครบครัน สำหรับระบบอัตโนมัติ**

### 🎯 **ระบบอัตโนมัติที่จะติดตั้ง:**
- ✅ **PayNoi Transaction Verification** - ตรวจสอบ PayNoi ทุก 5 วินาที
- ✅ **Manual Slip Verification** - ตรวจสอบและจับคู่สลิปกับ PayNoi ทุก 5 วินาที
- ✅ **User Expiration Management** - ตรวจสอบและปิด Jellyfin users ที่หมดอายุทุกนาที
- ✅ **System Health Monitoring** - ตรวจสอบระบบทุก 15 นาที
- ✅ **Automated Maintenance** - ทำความสะอาดและบำรุงรักษาอัตโนมัติ

### 📋 **วิธีการติดตั้ง (เลือก 1 วิธี):**

---

## **วิธีที่ 1: ใช้ Script อัตโนมัติ (แนะนำ)**

```bash
# 1. ให้สิทธิ์ execute
chmod +x setup_paynoi_cron_5sec.sh

# 2. รันการติดตั้ง
sudo ./setup_paynoi_cron_5sec.sh
```

---

## **วิธีที่ 2: ติดตั้งด้วยไฟล์ crontab**

```bash
# 1. สร้าง log directory
sudo mkdir -p /var/log/jellyfin
sudo chown www-data:www-data /var/log/jellyfin
sudo chmod 755 /var/log/jellyfin

# 2. ติดตั้ง crontab
sudo crontab -u www-data paynoi_crontab_5sec.txt
```

---

## **วิธีที่ 3: ติดตั้งแบบ Manual**

```bash
# 1. เปิด crontab editor
sudo crontab -u www-data -e

# 2. เพิ่มบรรทัดเหล่านี้:
```

```cron
# ============================================
# PAYNOI TRANSACTION VERIFICATION - EVERY 5 SECONDS
# ============================================
# Fetches PayNoi transactions and auto-verifies payments
* * * * * /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/paynoi-transactions.php >> /var/log/jellyfin/payment_cron.log 2>&1

# ============================================
# MANUAL SLIP VERIFICATION - EVERY 5 SECONDS
# ============================================
# Checks manual payment slips and matches with PayNoi amounts for approval
* * * * * /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/manual_check_payments.php >> /var/log/jellyfin/manual_cron.log 2>&1

# ============================================
# USER EXPIRATION CHECK - EVERY 1 MINUTE
# ============================================
# Checks for expired users and disables them in Jellyfin
* * * * * /usr/bin/php /var/www/html/cron/check_expiration.php >> /var/log/jellyfin/expiration_cron.log 2>&1

# ============================================
# SYSTEM HEALTH CHECK - EVERY 15 MINUTES
# ============================================
# Monitors system health, database, and API connectivity
*/15 * * * * /usr/bin/php /var/www/html/cron/linux_health_check.php >> /var/log/jellyfin/health_cron.log 2>&1
```

---

## ⏰ **ตารางการทำงานของระบบอัตโนมัติ**

| **งาน** | **ความถี่** | **จำนวนครั้งต่อนาที** | **จำนวนครั้งต่อชั่วโมง** | **คำอธิบาย** |
|---------|-------------|----------------------|------------------------|--------------|
| **PayNoi Transaction Verification** | ทุก 5 วินาที | 12 ครั้ง | 720 ครั้ง | ดึงข้อมูลจาก PayNoi และ auto-verify payments |
| **Manual Slip Verification** | ทุก 5 วินาที | 12 ครั้ง | 720 ครั้ง | ตรวจสอบสลิปและจับคู่กับ PayNoi เพื่ออนุมัติ |
| **User Expiration Check** | ทุก 1 นาที | 1 ครั้ง | 60 ครั้ง | ตรวจสอบและปิด Jellyfin users ที่หมดอายุ |
| **System Health Monitor** | ทุก 15 นาที | 0.07 ครั้ง | 4 ครั้ง | ตรวจสอบสุขภาพระบบและ API |
| **Log Cleanup** | ทุกวันเวลา 02:00 | - | - | ทำความสะอาด log files |
| **Weekly Maintenance** | อาทิตย์เวลา 03:00 | - | - | บำรุงรักษาระบบและ database |

---

## 📊 **การตรวจสอบและ Monitoring**

### **ตรวจสอบสถานะ:**
```bash
# ดู cron jobs ที่ติดตั้งแล้ว
sudo crontab -u www-data -l

# ตรวจสอบสถานะ cron service
sudo systemctl status cron

# ดู log files แบบ real-time
sudo tail -f /var/log/jellyfin/payment_cron.log      # PayNoi transactions
sudo tail -f /var/log/jellyfin/manual_cron.log       # Manual slip verification
sudo tail -f /var/log/jellyfin/expiration_cron.log   # User expiration
sudo tail -f /var/log/jellyfin/health_cron.log       # System health
sudo tail -f /var/log/jellyfin/*.log                 # All logs together
```

### **ตรวจสอบการทำงาน:**
```bash
# ดู log entries ล่าสุด
sudo tail -n 50 /var/log/jellyfin/payment_cron.log
sudo tail -n 50 /var/log/jellyfin/manual_cron.log
sudo tail -n 50 /var/log/jellyfin/expiration_cron.log

# ตรวจสอบ cron system logs
sudo journalctl -u cron -f

# ดูสถิติการทำงาน
sudo grep "PayNoi Transactions Sync - Completed" /var/log/jellyfin/payment_cron.log | tail -10
sudo grep "Manual Payment Check - Completed" /var/log/jellyfin/manual_cron.log | tail -10
sudo grep "Expiration Check - Completed" /var/log/jellyfin/expiration_cron.log | tail -10

# ทดสอบ scripts แบบ manual
sudo -u www-data php /var/www/html/paynoi-transactions.php
sudo -u www-data php /var/www/html/manual_check_payments.php
sudo -u www-data php /var/www/html/cron/check_expiration.php
```

---

## 🔧 **การแก้ไขปัญหา**

### **ปัญหาที่พบบ่อย:**

#### **1. Cron Jobs ไม่ทำงาน**
```bash
# ตรวจสอบ cron service
sudo systemctl restart cron
sudo systemctl status cron

# ตรวจสอบ permissions
sudo chown www-data:www-data /var/log/jellyfin
sudo chmod 755 /var/log/jellyfin
```

#### **2. PHP Script Error**
```bash
# ทดสอบ script แบบ manual
sudo -u www-data php /var/www/html/paynoi-transactions.php
sudo -u www-data php /var/www/html/manual_check_payments.php
```

#### **3. Log Files ใหญ่เกินไป**
```bash
# ลบ log files เก่า
sudo truncate -s 0 /var/log/jellyfin/payment_cron.log
sudo truncate -s 0 /var/log/jellyfin/manual_cron.log
```

---

## ⚠️ **ข้อควรระวัง**

### **Performance Impact:**
- **CPU Usage**: จะมีการใช้ CPU สูงขึ้นเนื่องจากรันทุก 5 วินาที
- **Database Load**: จะมี database queries มากขึ้น (720 ครั้งต่อชั่วโมง)
- **Log Files**: Log files จะเติบโตเร็วมาก

### **Recommendations:**
- **Monitor Server Resources**: ตรวจสอบ CPU และ Memory usage
- **Database Optimization**: ใช้ indexes ที่เหมาะสม
- **Log Rotation**: ตั้งค่า log rotation เพื่อป้องกัน disk full

---

## ✅ **การยืนยันการติดตั้งสำเร็จ**

### **ตรวจสอบว่าระบบทำงานปกติ:**
```bash
# 1. ดู cron jobs
sudo crontab -u www-data -l | grep paynoi-transactions

# 2. ตรวจสอบ log files มีการอัปเดต
ls -la /var/log/jellyfin/

# 3. ดู log entries ล่าสุด
sudo tail -f /var/log/jellyfin/payment_cron.log

# 4. ควรเห็น log entries ใหม่ทุก 5 วินาที
```

---

## 🎯 **Success Criteria**

**ระบบทำงานถูกต้องเมื่อ:**
- ✅ เห็น log entries ใหม่ทุก 5 วินาทีในไฟล์ `/var/log/jellyfin/payment_cron.log`
- ✅ PayNoi transactions ถูกตรวจสอบและ verify อัตโนมัติ
- ✅ Manual payments ถูกตรวจสอบและ verify อัตโนมัติ
- ✅ User expiration ถูกตรวจสอบทุกนาที
- ✅ ไม่มี error messages ใน log files

**🚀 ระบบอัตโนมัติครบครันพร้อมใช้งานแล้ว!**
- ✅ **PayNoi transactions** จะถูกตรวจสอบทุก 5 วินาที
- ✅ **Manual payment slips** จะถูกตรวจสอบและจับคู่ทุก 5 วินาที
- ✅ **Expired users** จะถูกปิดใน Jellyfin อัตโนมัติทุกนาที
- ✅ **System health** จะถูกตรวจสอบทุก 15 นาที
- ✅ **Maintenance** จะทำงานอัตโนมัติตามกำหนด

**🎬 Jellyfin by James ตอนนี้เป็นระบบอัตโนมัติ 100% แล้ว!**
