<?php
/**
 * Manual Payment Check Script
 * Used by AutoPaymentChecker for manual payment verification
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';

// Set content type for JSON output
header('Content-Type: application/json');

try {
    // Check database connection
    if (!isset($pdo)) {
        throw new Exception("Database connection not available");
    }

    // Get pending payment transactions (simplified query)
    $stmt = $pdo->prepare("
        SELECT pt.id, pt.user_id, pt.amount, pt.created_at, pt.status,
               u.username, u.phone,
               us.package_id, us.id as subscription_id,
               p.name as package_name, p.price, p.duration_days
        FROM payment_transactions pt
        LEFT JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY pt.created_at DESC
        LIMIT 50
    ");

    $stmt->execute();
    $pending_payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fetch PayNoi transactions from API
    require_once 'includes/PayNoiAPIClass.php';
    $paynoiAPI = new PayNoiAPI();

    // Get PayNoi transactions from last 24 hours
    $startDate = date('Y-m-d H:i:s', strtotime('-24 hours'));
    $endDate = date('Y-m-d H:i:s');

    $paynoiTransactions = $paynoiAPI->getTransactions($startDate, $endDate);
    $recent_transactions = [];

    if ($paynoiTransactions && isset($paynoiTransactions['data'])) {
        // Flatten PayNoi transactions
        foreach ($paynoiTransactions['data'] as $transactionGroup) {
            foreach ($transactionGroup as $transaction) {
                if (floatval($transaction['amount'] ?? 0) > 0) { // Only incoming transactions
                    $recent_transactions[] = [
                        'amount' => floatval($transaction['amount']),
                        'created_at' => $transaction['date'],
                        'id' => $transaction['id'] ?? $transaction['trans_id'] ?? '',
                        'type' => $transaction['type'] ?? 'transfer',
                        'bankaccount' => $transaction['bankaccount'] ?? ''
                    ];
                }
            }
        }
    }

    // Check for potential matches
    $potential_matches = [];
    
    foreach ($pending_payments as $payment) {
        foreach ($recent_transactions as $transaction) {
            // Check if amounts match (within 1 baht tolerance)
            if (abs($transaction['amount'] - $payment['amount']) <= 1.00) {
                // Check if times are close (within 2 hours)
                $payment_time = strtotime($payment['created_at']);
                $transaction_time = strtotime($transaction['created_at']);
                $time_diff = abs($payment_time - $transaction_time);
                
                if ($time_diff <= 7200) { // 2 hours
                    $potential_matches[] = [
                        'payment' => $payment,
                        'transaction' => $transaction,
                        'time_diff_minutes' => round($time_diff / 60),
                        'amount_diff' => abs($transaction['amount'] - $payment['amount'])
                    ];
                }
            }
        }
    }

    // Auto-match obvious matches (exact amount, close time)
    $auto_matched = 0;
    $manual_review = [];
    
    foreach ($potential_matches as $match) {
        $payment = $match['payment'];
        $transaction = $match['transaction'];
        
        // Auto-match if exact amount and within 30 minutes
        if ($match['amount_diff'] == 0 && $match['time_diff_minutes'] <= 30) {
            try {
                // Update payment transaction status
                $stmt = $pdo->prepare("
                    UPDATE payment_transactions
                    SET status = 'completed',
                        transaction_ref = ?,
                        paid_at = NOW(),
                        updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$transaction['trans_id'] ?? $transaction['id'], $payment['id']]);
                
                // Update subscription and user account
                if ($payment['user_id'] && $payment['duration_days']) {
                    // Calculate expiration date
                    $expiration_date = date('Y-m-d H:i:s', strtotime("+{$payment['duration_days']} days"));

                    // Update user subscription
                    $stmt = $pdo->prepare("
                        UPDATE user_subscriptions
                        SET status = 'active',
                            start_date = NOW(),
                            end_date = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$expiration_date, $payment['subscription_id']]);

                    // Update user account
                    $stmt = $pdo->prepare("
                        UPDATE users
                        SET expires_at = ?,
                            status = 'active',
                            package_id = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$expiration_date, $payment['package_id'], $payment['user_id']]);
                    
                    // Enable in Jellyfin if API is available
                    try {
                        require_once 'includes/JellyfinAPI.php';
                        $jellyfin = new JellyfinAPI();
                        $jellyfin->enableUser($payment['username']);
                    } catch (Exception $e) {
                        error_log("Jellyfin API error: " . $e->getMessage());
                    }

                    // Log activity
                    if (function_exists('log_activity')) {
                        log_activity($payment['user_id'], 'payment_verified',
                            "Payment auto-verified: {$payment['amount']} THB for {$payment['package_name']}");
                    }
                }
                
                $auto_matched++;
                
            } catch (Exception $e) {
                error_log("Auto-match error: " . $e->getMessage());
                $manual_review[] = $match;
            }
        } else {
            $manual_review[] = $match;
        }
    }

    // Get updated counts
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_pending,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as pending_24h,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as pending_1h
        FROM payment_transactions
        WHERE status = 'pending'
    ");
    $stmt->execute();
    $payment_stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // Prepare response
    $response = [
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'stats' => [
            'total_pending' => (int)$payment_stats['total_pending'],
            'pending_24h' => (int)$payment_stats['pending_24h'],
            'pending_1h' => (int)$payment_stats['pending_1h'],
            'auto_matched' => $auto_matched,
            'manual_review' => count($manual_review),
            'potential_matches' => count($potential_matches)
        ],
        'auto_matched_count' => $auto_matched,
        'manual_review_needed' => count($manual_review),
        'message' => $auto_matched > 0 
            ? "Auto-matched {$auto_matched} payments successfully" 
            : "No automatic matches found"
    ];

    // Add manual review items if any
    if (!empty($manual_review)) {
        $response['manual_review_items'] = array_slice($manual_review, 0, 10); // Limit to 10 items
    }

    echo json_encode($response, JSON_PRETTY_PRINT);

} catch (Exception $e) {
    // Log error
    error_log("Manual check payments error: " . $e->getMessage());
    
    // Return error response
    $error_response = [
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s'),
        'stats' => [
            'total_pending' => 0,
            'pending_24h' => 0,
            'pending_1h' => 0,
            'auto_matched' => 0,
            'manual_review' => 0,
            'potential_matches' => 0
        ]
    ];
    
    echo json_encode($error_response, JSON_PRETTY_PRINT);
    exit(1);
}
?>
