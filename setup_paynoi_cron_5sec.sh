#!/bin/bash

# Setup PayNoi Cron Jobs - Every 5 seconds
# This script sets up cron jobs to run PayNoi transaction checks every 5 seconds

echo "🎬 Setting up PayNoi Cron Jobs - Every 5 seconds"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WEB_ROOT="/var/www/html"
LOG_DIR="/var/log/jellyfin"
CRON_USER="www-data"
PHP_BIN="/usr/bin/php"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root${NC}"
   echo "Usage: sudo ./setup_paynoi_cron_5sec.sh"
   exit 1
fi

echo -e "${BLUE}📋 Configuration:${NC}"
echo "  Web Root: $WEB_ROOT"
echo "  Log Directory: $LOG_DIR"
echo "  Cron User: $CRON_USER"
echo "  PHP Binary: $PHP_BIN"
echo ""

# Create log directory if it doesn't exist
if [ ! -d "$LOG_DIR" ]; then
    echo -e "${BLUE}📁 Creating log directory...${NC}"
    mkdir -p "$LOG_DIR"
    chown www-data:www-data "$LOG_DIR"
    chmod 755 "$LOG_DIR"
    echo -e "${GREEN}✅ Log directory created: $LOG_DIR${NC}"
fi

# Create temporary crontab file
CRONTAB_FILE="/tmp/paynoi_crontab_5sec"

echo -e "${BLUE}⏰ Creating cron jobs for every 5 seconds...${NC}"

cat > "$CRONTAB_FILE" << EOF
# PayNoi Transaction Check - Every 5 seconds (12 times per minute)
# Generated on $(date)

# PayNoi payment verification - every 5 seconds
* * * * * $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 5; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 10; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 15; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 20; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 25; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 30; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 35; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 40; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 45; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 50; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 55; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1

# Manual payment slip verification - every 5 seconds
* * * * * $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 5; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 10; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 15; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 20; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 25; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 30; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 35; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 40; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 45; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 50; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 55; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1

# User expiration check - every 1 minute
* * * * * $PHP_BIN $WEB_ROOT/check_expired_users.php >> $LOG_DIR/expiration_cron.log 2>&1

EOF

# Backup existing crontab
echo -e "${YELLOW}📝 Backing up existing crontab...${NC}"
crontab -u "$CRON_USER" -l > "/tmp/crontab_backup_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || echo "No existing crontab found"

# Install new crontab
echo -e "${YELLOW}📝 Installing new crontab for user: $CRON_USER${NC}"
crontab -u "$CRON_USER" "$CRONTAB_FILE"

# Clean up temporary file
rm "$CRONTAB_FILE"

echo -e "${GREEN}✅ Crontab installed successfully${NC}"

# Show installed cron jobs
echo -e "${BLUE}📋 Installed cron jobs:${NC}"
crontab -u "$CRON_USER" -l

echo ""
echo -e "${GREEN}🎉 PayNoi Cron Setup Completed!${NC}"
echo ""
echo -e "${BLUE}📊 Monitoring Commands:${NC}"
echo "  View cron jobs: crontab -u $CRON_USER -l"
echo "  Check PayNoi logs: tail -f $LOG_DIR/payment_cron.log"
echo "  Check manual logs: tail -f $LOG_DIR/manual_cron.log"
echo "  Check expiration logs: tail -f $LOG_DIR/expiration_cron.log"
echo ""
echo -e "${BLUE}🔧 Management Commands:${NC}"
echo "  Restart cron: systemctl restart cron"
echo "  Check cron status: systemctl status cron"
echo "  View system logs: journalctl -u cron -f"
echo ""
echo -e "${YELLOW}⚠️  Important Notes:${NC}"
echo "  - PayNoi transactions will be checked every 5 seconds"
echo "  - Manual payments will be checked every 5 seconds"
echo "  - User expiration will be checked every 1 minute"
echo "  - Check logs in $LOG_DIR for any issues"
echo "  - This will generate 12 checks per minute (720 per hour)"
echo ""
echo -e "${GREEN}✅ System is now running automated checks every 5 seconds!${NC}"
