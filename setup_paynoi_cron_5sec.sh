#!/bin/bash

# Setup PayNoi Cron Jobs - Every 5 seconds
# This script sets up cron jobs to run PayNoi transaction checks every 5 seconds

echo "🎬 Setting up PayNoi Cron Jobs - Every 5 seconds"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WEB_ROOT="/var/www/html"
LOG_DIR="/var/log/jellyfin"
CRON_USER="www-data"
PHP_BIN="/usr/bin/php"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root${NC}"
   echo "Usage: sudo ./setup_paynoi_cron_5sec.sh"
   exit 1
fi

echo -e "${BLUE}📋 Configuration:${NC}"
echo "  Web Root: $WEB_ROOT"
echo "  Log Directory: $LOG_DIR"
echo "  Cron User: $CRON_USER"
echo "  PHP Binary: $PHP_BIN"
echo ""

# Create log directory if it doesn't exist
if [ ! -d "$LOG_DIR" ]; then
    echo -e "${BLUE}📁 Creating log directory...${NC}"
    mkdir -p "$LOG_DIR"
    chown www-data:www-data "$LOG_DIR"
    chmod 755 "$LOG_DIR"
    echo -e "${GREEN}✅ Log directory created: $LOG_DIR${NC}"
fi

# Create temporary crontab file
CRONTAB_FILE="/tmp/paynoi_crontab_5sec"

echo -e "${BLUE}⏰ Creating cron jobs for every 5 seconds...${NC}"

cat > "$CRONTAB_FILE" << EOF
# Jellyfin by James - Complete Automated Cron Jobs
# Generated on $(date)
#
# This crontab includes:
# 1. PayNoi transaction verification (every 5 seconds)
# 2. Manual slip verification and matching (every 5 seconds)
# 3. User expiration checking and Jellyfin disable (every 1 minute)
# 4. System health monitoring (every 15 minutes)

# ============================================
# PAYNOI TRANSACTION VERIFICATION - EVERY 5 SECONDS
# ============================================
# Fetches PayNoi transactions and auto-verifies payments
* * * * * $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 5; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 10; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 15; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 20; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 25; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 30; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 35; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 40; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 45; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 50; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1
* * * * * sleep 55; $PHP_BIN $WEB_ROOT/paynoi-transactions.php >> $LOG_DIR/payment_cron.log 2>&1

# ============================================
# MANUAL SLIP VERIFICATION - EVERY 5 SECONDS
# ============================================
# Checks manual payment slips and matches with PayNoi amounts for approval
* * * * * $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 5; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 10; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 15; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 20; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 25; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 30; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 35; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 40; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 45; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 50; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1
* * * * * sleep 55; $PHP_BIN $WEB_ROOT/manual_check_payments.php >> $LOG_DIR/manual_cron.log 2>&1

# ============================================
# USER EXPIRATION CHECK - EVERY 1 MINUTE
# ============================================
# Checks for expired users and disables them in Jellyfin
* * * * * $PHP_BIN $WEB_ROOT/cron/check_expiration.php >> $LOG_DIR/expiration_cron.log 2>&1

# ============================================
# SYSTEM HEALTH CHECK - EVERY 15 MINUTES
# ============================================
# Monitors system health, database, and API connectivity
*/15 * * * * $PHP_BIN $WEB_ROOT/cron/linux_health_check.php >> $LOG_DIR/health_cron.log 2>&1

# ============================================
# LOG CLEANUP - DAILY AT 2 AM
# ============================================
# Prevents log files from growing too large
0 2 * * * find $LOG_DIR -name "*.log" -size +100M -exec truncate -s 10M {} \; >> $LOG_DIR/cleanup.log 2>&1

# ============================================
# WEEKLY MAINTENANCE - SUNDAY AT 3 AM
# ============================================
# Database optimization and system maintenance
0 3 * * 0 $PHP_BIN $WEB_ROOT/cron/linux_health_check.php >> $LOG_DIR/weekly_maintenance.log 2>&1

EOF

# Backup existing crontab
echo -e "${YELLOW}📝 Backing up existing crontab...${NC}"
crontab -u "$CRON_USER" -l > "/tmp/crontab_backup_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || echo "No existing crontab found"

# Install new crontab
echo -e "${YELLOW}📝 Installing new crontab for user: $CRON_USER${NC}"
crontab -u "$CRON_USER" "$CRONTAB_FILE"

# Clean up temporary file
rm "$CRONTAB_FILE"

echo -e "${GREEN}✅ Crontab installed successfully${NC}"

# Show installed cron jobs
echo -e "${BLUE}📋 Installed cron jobs:${NC}"
crontab -u "$CRON_USER" -l

echo ""
echo -e "${GREEN}🎉 Complete Jellyfin Automation Setup Completed!${NC}"
echo ""
echo -e "${BLUE}📊 Monitoring Commands:${NC}"
echo "  View all cron jobs: crontab -u $CRON_USER -l"
echo "  Check PayNoi logs: tail -f $LOG_DIR/payment_cron.log"
echo "  Check manual slip logs: tail -f $LOG_DIR/manual_cron.log"
echo "  Check expiration logs: tail -f $LOG_DIR/expiration_cron.log"
echo "  Check health logs: tail -f $LOG_DIR/health_cron.log"
echo "  View all logs: tail -f $LOG_DIR/*.log"
echo ""
echo -e "${BLUE}🔧 Management Commands:${NC}"
echo "  Restart cron: systemctl restart cron"
echo "  Check cron status: systemctl status cron"
echo "  View system logs: journalctl -u cron -f"
echo "  Test PayNoi manually: php $WEB_ROOT/paynoi-transactions.php"
echo "  Test slip check manually: php $WEB_ROOT/manual_check_payments.php"
echo "  Test expiration manually: php $WEB_ROOT/cron/check_expiration.php"
echo ""
echo -e "${YELLOW}⚠️  Automation Schedule:${NC}"
echo "  🔄 PayNoi transaction verification: Every 5 seconds (720/hour)"
echo "  📄 Manual slip verification & matching: Every 5 seconds (720/hour)"
echo "  ⏰ User expiration check & Jellyfin disable: Every 1 minute (60/hour)"
echo "  🏥 System health monitoring: Every 15 minutes (4/hour)"
echo "  🧹 Log cleanup: Daily at 2:00 AM"
echo "  🔧 Weekly maintenance: Sunday at 3:00 AM"
echo ""
echo -e "${BLUE}🎯 What This System Does:${NC}"
echo "  ✅ Auto-verifies PayNoi payments and enables Jellyfin users"
echo "  ✅ Matches manual payment slips with PayNoi amounts for approval"
echo "  ✅ Automatically disables expired Jellyfin users"
echo "  ✅ Monitors system health and API connectivity"
echo "  ✅ Manages log files and performs maintenance"
echo ""
echo -e "${GREEN}🚀 Jellyfin by James is now fully automated!${NC}"
echo -e "${GREEN}   All payments, slips, and user management are handled automatically${NC}"
