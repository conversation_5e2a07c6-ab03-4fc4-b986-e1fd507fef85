<?php
/**
 * Fix Time Filtering Issues
 * แก้ไขปัญหาการกรองเวลาที่ทำให้ transactions หายไป
 */

require_once 'includes/config.php';
require_once 'includes/PayNoiAPIClass.php';

echo "🔧 Fix Time Filtering Issues\n";
echo "============================\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "1. 📋 Getting pending payments...\n";
    
    // Get pending payments
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, u.phone, u.jellyfin_user_id,
               us.package_id, us.id as subscription_id,
               p.name as package_name, p.price as package_price, p.duration_days
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status IN ('pending_verification', 'pending')
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.created_at DESC
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   Found " . count($pendingPayments) . " pending payments\n\n";
    
    if (count($pendingPayments) == 0) {
        echo "✅ No pending payments to process\n";
        exit(0);
    }
    
    foreach ($pendingPayments as $payment) {
        echo "   - {$payment['username']}: {$payment['amount']} THB at {$payment['created_at']}\n";
    }
    
    echo "\n2. 💰 Getting PayNoi transactions (NO time filtering)...\n";
    
    // Initialize PayNoi API
    $paynoiAPI = new PayNoiAPI();
    $response = $paynoiAPI->getTransactions();
    
    if (!$response || !isset($response['data'])) {
        echo "❌ Failed to fetch PayNoi transactions\n";
        exit(1);
    }
    
    // Flatten ALL PayNoi transactions (no time filtering)
    $allTransactions = [];
    foreach ($response['data'] as $transactionGroup) {
        foreach ($transactionGroup as $transaction) {
            if (floatval($transaction['amount'] ?? 0) > 0) { // Only incoming transactions
                $allTransactions[] = $transaction;
            }
        }
    }
    
    echo "   Found " . count($allTransactions) . " total incoming PayNoi transactions\n";
    
    if (count($allTransactions) == 0) {
        echo "❌ No PayNoi transactions found\n";
        exit(1);
    }
    
    echo "\n3. 🔍 Processing matches (extended search)...\n";
    
    $totalMatched = 0;
    $totalAutoVerified = 0;
    
    foreach ($pendingPayments as $payment) {
        $paymentAmount = floatval($payment['amount']);
        $paymentTime = strtotime($payment['created_at']);
        
        echo "\n   Processing Payment #{$payment['id']} - {$payment['username']} - {$paymentAmount} THB\n";
        
        $bestMatch = null;
        $bestScore = PHP_FLOAT_MAX;
        $allMatches = [];
        
        foreach ($allTransactions as $transaction) {
            $transactionAmount = floatval($transaction['amount'] ?? 0);
            $transactionTime = strtotime($transaction['date'] ?? '');
            
            // Check amount tolerance (±1.00 THB)
            $amountDiff = abs($transactionAmount - $paymentAmount);
            if ($amountDiff > 1.00) {
                continue;
            }
            
            // Calculate time difference (but don't filter by it yet)
            $timeDiff = $transactionTime ? abs($transactionTime - $paymentTime) : PHP_INT_MAX;
            $timeDiffHours = round($timeDiff / 3600, 1);
            
            // Calculate match score (lower is better)
            $score = $amountDiff + ($timeDiff / 86400); // Amount diff + time diff in days
            
            $match = [
                'transaction' => $transaction,
                'amount_diff' => $amountDiff,
                'time_diff_hours' => $timeDiffHours,
                'score' => $score
            ];
            
            $allMatches[] = $match;
            
            if ($score < $bestScore) {
                $bestScore = $score;
                $bestMatch = $match;
            }
        }
        
        if ($bestMatch) {
            $totalMatched++;
            $transaction = $bestMatch['transaction'];
            $transactionTime = date('Y-m-d H:i:s', strtotime($transaction['date']));
            
            echo "      ✅ Found match: {$transaction['amount']} THB at {$transactionTime}\n";
            echo "         Amount diff: {$bestMatch['amount_diff']} THB\n";
            echo "         Time diff: {$bestMatch['time_diff_hours']} hours\n";
            
            // Show all matches for this payment
            if (count($allMatches) > 1) {
                echo "         Other potential matches:\n";
                usort($allMatches, function($a, $b) { return $a['score'] <=> $b['score']; });
                foreach (array_slice($allMatches, 1, 3) as $i => $match) {
                    $t = $match['transaction'];
                    $tTime = date('Y-m-d H:i:s', strtotime($t['date']));
                    echo "           #{$i+2}: {$t['amount']} THB at {$tTime} (diff: {$match['amount_diff']} THB, {$match['time_diff_hours']}h)\n";
                }
            }
            
            // Extended auto-verification rules (more lenient)
            $autoVerify = false;
            $reason = "";
            
            if ($bestMatch['amount_diff'] <= 0.01) {
                // Exact amount match
                $autoVerify = true;
                $reason = "Exact amount match";
            } elseif ($bestMatch['amount_diff'] <= 0.50 && $bestMatch['time_diff_hours'] <= 72) {
                // Very close amount within 3 days
                $autoVerify = true;
                $reason = "Very close amount within 3 days";
            } elseif ($paymentAmount <= 100.00 && $bestMatch['amount_diff'] <= 1.00) {
                // Small amount with good match
                $autoVerify = true;
                $reason = "Small amount with acceptable difference";
            } else {
                // Check user history
                $historyStmt = $pdo->prepare("
                    SELECT COUNT(*) as verified_count FROM payment_transactions
                    WHERE user_id = ? AND status IN ('completed', 'verified')
                ");
                $historyStmt->execute([$payment['user_id']]);
                $verifiedCount = $historyStmt->fetch()['verified_count'];
                
                if ($verifiedCount >= 1 && $bestMatch['amount_diff'] <= 1.00) {
                    $autoVerify = true;
                    $reason = "Trusted user with acceptable difference";
                } else {
                    $reason = "Manual review needed (large difference or new user)";
                }
            }
            
            if ($autoVerify) {
                echo "         🎯 AUTO-VERIFYING: {$reason}\n";
                
                // Update payment status
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions 
                    SET status = 'verified', 
                        verified_at = NOW(),
                        admin_notes = ?,
                        paynoi_transaction_id = ?
                    WHERE id = ?
                ");
                $adminNotes = "Auto-verified (extended search): {$reason}. PayNoi match: {$transaction['amount']} THB, diff: {$bestMatch['amount_diff']} THB, time: {$bestMatch['time_diff_hours']}h";
                $updateStmt->execute([
                    $adminNotes,
                    $transaction['id'] ?? $transaction['trans_id'] ?? '',
                    $payment['id']
                ]);
                
                // Enable Jellyfin user if needed
                if (!empty($payment['jellyfin_user_id'])) {
                    try {
                        require_once 'includes/JellyfinAPI.php';
                        $jellyfinAPI = new JellyfinAPI();
                        $result = $jellyfinAPI->enableUser($payment['jellyfin_user_id']);
                        if ($result) {
                            echo "         🎬 Enabled Jellyfin user\n";
                        }
                    } catch (Exception $e) {
                        echo "         ⚠️  Jellyfin API error: " . $e->getMessage() . "\n";
                    }
                }
                
                // Update subscription dates
                if ($payment['subscription_id']) {
                    $subscriptionStmt = $pdo->prepare("
                        UPDATE user_subscriptions 
                        SET start_date = NOW(), 
                            end_date = DATE_ADD(NOW(), INTERVAL ? DAY),
                            status = 'active'
                        WHERE id = ?
                    ");
                    $subscriptionStmt->execute([
                        $payment['duration_days'] ?? 30,
                        $payment['subscription_id']
                    ]);
                    echo "         📅 Updated subscription ({$payment['duration_days']} days)\n";
                }
                
                // Award affiliate points if referred
                $referrerStmt = $pdo->prepare("SELECT referred_by FROM users WHERE id = ?");
                $referrerStmt->execute([$payment['user_id']]);
                $referrer = $referrerStmt->fetch();
                
                if ($referrer && $referrer['referred_by']) {
                    $pointsStmt = $pdo->prepare("
                        UPDATE users 
                        SET affiliate_points = affiliate_points + 15 
                        WHERE id = ?
                    ");
                    $pointsStmt->execute([$referrer['referred_by']]);
                    
                    // Log referral transaction
                    try {
                        $refLogStmt = $pdo->prepare("
                            INSERT INTO referral_transactions (referrer_id, referred_user_id, points_awarded, transaction_type, created_at)
                            VALUES (?, ?, 15, 'package_purchase', NOW())
                        ");
                        $refLogStmt->execute([$referrer['referred_by'], $payment['user_id']]);
                        echo "         🎁 Awarded 15 affiliate points\n";
                    } catch (Exception $e) {
                        echo "         ⚠️  Affiliate points error: " . $e->getMessage() . "\n";
                    }
                }
                
                $totalAutoVerified++;
                
            } else {
                echo "         ⚠️  MANUAL REVIEW: {$reason}\n";
                
                // Update to manual review status
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions 
                    SET status = 'manual_review',
                        admin_notes = ?
                    WHERE id = ?
                ");
                $adminNotes = "Manual review needed (extended search): {$reason}. PayNoi match: {$transaction['amount']} THB, diff: {$bestMatch['amount_diff']} THB, time: {$bestMatch['time_diff_hours']}h";
                $updateStmt->execute([$adminNotes, $payment['id']]);
            }
            
        } else {
            echo "      ❌ No matching PayNoi transaction found\n";
        }
    }
    
    echo "\n4. 📊 Summary:\n";
    echo "==============\n";
    echo "Total pending payments: " . count($pendingPayments) . "\n";
    echo "Total matched: {$totalMatched}\n";
    echo "Total auto-verified: {$totalAutoVerified}\n";
    echo "Manual review needed: " . ($totalMatched - $totalAutoVerified) . "\n";
    echo "\n";
    
    if ($totalAutoVerified > 0) {
        echo "✅ Successfully auto-verified {$totalAutoVerified} payments!\n";
        echo "🎬 Users should now be able to access Jellyfin\n";
        echo "💡 Check Jellyfin admin panel to confirm users are enabled\n";
    }
    
    if ($totalMatched < count($pendingPayments)) {
        echo "⚠️  Some payments couldn't be matched\n";
        echo "💡 These may need manual verification in admin panel\n";
    }
    
    if ($totalMatched > 0) {
        echo "\n🎯 Next steps:\n";
        echo "1. Test Jellyfin login with verified users\n";
        echo "2. Update cron to use extended time range\n";
        echo "3. Monitor future payments for similar issues\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
