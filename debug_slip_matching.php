<?php
/**
 * Debug Slip Matching Issues
 * ตรวจสอบว่าทำไมสลิปไม่ถูก match กับ PayNoi transactions
 */

require_once 'includes/config.php';
require_once 'includes/PayNoiAPIClass.php';

echo "🔍 Debug Slip Matching Issues\n";
echo "============================\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Initialize PayNoi API
    $paynoiAPI = new PayNoiAPI();
    
    echo "1. 📋 Checking pending payments...\n";
    
    // Get pending payments
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, u.phone,
               us.package_id, us.id as subscription_id,
               p.name as package_name, p.price as package_price, p.duration_days
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status IN ('pending_verification', 'pending')
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.created_at DESC
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $pendingCount = count($pendingPayments);
    echo "   Found {$pendingCount} pending payments\n\n";
    
    if ($pendingCount == 0) {
        echo "❌ No pending payments found\n";
        exit(0);
    }
    
    // Show pending payments details
    echo "📋 Pending Payments Details:\n";
    echo "----------------------------\n";
    foreach ($pendingPayments as $i => $payment) {
        $paymentTime = date('Y-m-d H:i:s', strtotime($payment['created_at']));
        echo "Payment #" . ($i + 1) . ":\n";
        echo "  - ID: {$payment['id']}\n";
        echo "  - User: {$payment['username']}\n";
        echo "  - Amount: {$payment['amount']} THB\n";
        echo "  - Created: {$paymentTime}\n";
        echo "  - Status: {$payment['status']}\n";
        echo "  - Package: {$payment['package_name']} ({$payment['package_price']} THB)\n";
        echo "  - Has Slip: " . (!empty($payment['slip_image']) ? 'Yes' : 'No') . "\n";
        echo "\n";
    }
    
    echo "2. 💰 Fetching PayNoi transactions...\n";

    // Get PayNoi transactions (API doesn't support date range filtering)
    echo "   Fetching all available transactions...\n";

    $paynoiTransactions = $paynoiAPI->getTransactions();
    
    if (!$paynoiTransactions || !isset($paynoiTransactions['data'])) {
        echo "❌ Failed to fetch PayNoi transactions\n";
        echo "   Response: " . json_encode($paynoiTransactions) . "\n";
        exit(1);
    }
    
    // Flatten PayNoi transactions and filter by date (last 24 hours)
    $flatTransactions = [];
    $cutoffTime = strtotime('-24 hours');

    foreach ($paynoiTransactions['data'] as $transactionGroup) {
        foreach ($transactionGroup as $transaction) {
            if (floatval($transaction['amount'] ?? 0) > 0) { // Only incoming transactions
                // Filter by date (last 24 hours)
                $transactionTime = strtotime($transaction['date'] ?? '');
                if ($transactionTime >= $cutoffTime) {
                    $flatTransactions[] = $transaction;
                }
            }
        }
    }
    
    $transactionCount = count($flatTransactions);
    echo "   Found {$transactionCount} incoming PayNoi transactions\n\n";
    
    if ($transactionCount == 0) {
        echo "❌ No incoming PayNoi transactions found\n";
        echo "   This is likely why no matches were found\n";
        exit(0);
    }
    
    // Show PayNoi transactions
    echo "💰 PayNoi Transactions (last 10):\n";
    echo "----------------------------------\n";
    $displayTransactions = array_slice($flatTransactions, 0, 10);
    foreach ($displayTransactions as $i => $transaction) {
        $transactionTime = date('Y-m-d H:i:s', strtotime($transaction['date'] ?? ''));
        echo "Transaction #" . ($i + 1) . ":\n";
        echo "  - Amount: {$transaction['amount']} THB\n";
        echo "  - Date: {$transactionTime}\n";
        echo "  - Type: {$transaction['type']}\n";
        echo "  - Bank: {$transaction['bankaccount']}\n";
        echo "\n";
    }
    
    echo "3. 🔍 Analyzing potential matches...\n";
    echo "------------------------------------\n";
    
    $totalMatches = 0;
    
    foreach ($pendingPayments as $paymentIndex => $payment) {
        $paymentAmount = floatval($payment['amount']);
        $paymentTime = strtotime($payment['created_at']);
        
        echo "Analyzing Payment #{$payment['id']} ({$payment['username']} - {$paymentAmount} THB):\n";
        
        $matches = [];
        
        foreach ($flatTransactions as $transactionIndex => $transaction) {
            $transactionAmount = floatval($transaction['amount'] ?? 0);
            $transactionTime = strtotime($transaction['date'] ?? '');
            
            // Check amount match (within 1.00 tolerance)
            $amountDiff = abs($transactionAmount - $paymentAmount);
            if ($amountDiff > 1.00) {
                continue;
            }
            
            // Check time match (within 2 hours)
            $timeDiff = abs($transactionTime - $paymentTime);
            if ($timeDiff > 7200) { // 2 hours
                continue;
            }
            
            $timeDiffMinutes = round($timeDiff / 60);
            $matches[] = [
                'transaction' => $transaction,
                'amount_diff' => $amountDiff,
                'time_diff_minutes' => $timeDiffMinutes,
                'score' => $amountDiff + ($timeDiff / 3600) // Amount diff + time diff in hours
            ];
        }
        
        if (empty($matches)) {
            echo "  ❌ No potential matches found\n";
            echo "     Reasons could be:\n";
            echo "     - Amount difference > 1.00 THB\n";
            echo "     - Time difference > 2 hours\n";
            echo "     - No PayNoi transactions in the time range\n";
        } else {
            // Sort by best score (lowest is best)
            usort($matches, function($a, $b) {
                return $a['score'] <=> $b['score'];
            });
            
            echo "  ✅ Found " . count($matches) . " potential matches:\n";
            foreach ($matches as $i => $match) {
                $transaction = $match['transaction'];
                $transactionTime = date('Y-m-d H:i:s', strtotime($transaction['date']));
                echo "     Match #" . ($i + 1) . ":\n";
                echo "       - Amount: {$transaction['amount']} THB (diff: {$match['amount_diff']} THB)\n";
                echo "       - Time: {$transactionTime} (diff: {$match['time_diff_minutes']} minutes)\n";
                echo "       - Score: " . round($match['score'], 3) . "\n";
                
                // Check auto-verification rules
                $autoVerify = false;
                $reason = "";
                
                if ($match['amount_diff'] <= 0.50 && $match['time_diff_minutes'] <= 30) {
                    $autoVerify = true;
                    $reason = "Exact match within 30 minutes";
                } elseif ($paymentAmount <= 100.00 && $match['amount_diff'] <= 1.00 && $match['time_diff_minutes'] <= 60) {
                    $autoVerify = true;
                    $reason = "Small amount with good match";
                } else {
                    // Check user history
                    $historyStmt = $pdo->prepare("
                        SELECT COUNT(*) as verified_count FROM payment_transactions
                        WHERE user_id = ? AND status IN ('completed', 'verified')
                    ");
                    $historyStmt->execute([$payment['user_id']]);
                    $verifiedCount = $historyStmt->fetch()['verified_count'];
                    
                    if ($verifiedCount >= 3 && $match['amount_diff'] <= 1.00 && $match['time_diff_minutes'] <= 120) {
                        $autoVerify = true;
                        $reason = "Trusted user ({$verifiedCount} previous payments)";
                    } else {
                        $reason = "Manual review required (amount: {$paymentAmount} THB, diff: {$match['amount_diff']}, time: {$match['time_diff_minutes']}min, history: {$verifiedCount})";
                    }
                }
                
                echo "       - Auto-verify: " . ($autoVerify ? "✅ YES" : "⚠️ NO") . "\n";
                echo "       - Reason: {$reason}\n";
                echo "\n";
            }
            $totalMatches += count($matches);
        }
        echo "\n";
    }
    
    echo "4. 📊 Summary:\n";
    echo "==============\n";
    echo "Pending Payments: {$pendingCount}\n";
    echo "PayNoi Transactions: {$transactionCount}\n";
    echo "Total Potential Matches: {$totalMatches}\n";
    echo "\n";
    
    if ($totalMatches == 0) {
        echo "🔍 Possible reasons for no matches:\n";
        echo "1. PayNoi transactions are not in the expected time range\n";
        echo "2. Amount differences are too large (>1.00 THB)\n";
        echo "3. Time differences are too large (>2 hours)\n";
        echo "4. PayNoi API is not returning recent transactions\n";
        echo "5. Payment amounts don't include the random decimal (0.01-0.99)\n";
        echo "\n";
        echo "💡 Recommendations:\n";
        echo "1. Check if PayNoi API is working correctly\n";
        echo "2. Verify payment amounts include random decimals\n";
        echo "3. Check if payment times are recorded correctly\n";
        echo "4. Consider increasing tolerance values if needed\n";
    } else {
        echo "✅ Matches found! The auto slip verification should work.\n";
        echo "💡 If matches aren't being processed automatically, check:\n";
        echo "1. Is auto_slip_verification.php running via cron?\n";
        echo "2. Are there any PHP errors in the logs?\n";
        echo "3. Are the database updates working correctly?\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
